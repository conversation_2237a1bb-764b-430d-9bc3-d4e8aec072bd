import { useRouter, useSegments, Slot } from 'expo-router';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import ROUTES from '../config/routes';
import { ActivityIndicator, View } from 'react-native';

export default function RootLayout() {
    const { isAuthenticated } = useSelector((state) => state.auth);
    const segments = useSegments();
    const router = useRouter();

    const [isReady, setIsReady] = useState(false);
    const [hasNavigated, setHasNavigated] = useState(false);

    useEffect(() => {
        setIsReady(true);
    }, []);

    useEffect(() => {
        if (!isReady) return;
        const inPublic = segments[0] === 'publicRoutes';
        const inSitemap = segments[0] === '_sitemap' || segments.length === 0;

        if (!isAuthenticated && !inPublic) {
            router.replace(ROUTES.PUBLIC.LOGIN);
            setHasNavigated(true);
        } else if (isAuthenticated && (inPublic || inSitemap)) {
            router.replace(ROUTES.PRIVATE.HOME);
            setHasNavigated(true);
        } else {
            setHasNavigated(true);
        }
    }, [isAuthenticated, segments, isReady]);



    if (!hasNavigated) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color="#7FC5C6" />
            </View>
        );
    }

    return <Slot />;
}
