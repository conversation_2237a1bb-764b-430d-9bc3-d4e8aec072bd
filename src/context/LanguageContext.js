import React, { createContext, useContext, useEffect, useState } from "react";
import { getLocales } from "expo-localization";
import { I18n } from "i18n-js";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { AppState } from "react-native";
import languageFile from "../assets/language.json";
const LanguageContext = createContext();

const i18n = new I18n();

i18n.locale = getLocales()[0].languageCode == "th" ? "th" : "en";
i18n.enableFallback = true;
const TRANSLATIONURL = process.env.TRANSLATIONS_API_URL;
export const LanguageProvider = ({ children }) => {
    const [locale, setLocale] = useState(i18n.locale);
    const [language, setLanguage] = useState(i18n.locale);
    const [appStateChange, setAppStateChange] = useState(false);
    const [currentLanguage, setCurrentLanguage] = useState("");

    const loadTranslations = async () => {
        try {
            i18n.translations = {
                en: languageFile.en,
                th: languageFile.th,
            };
            const response = await fetch(process.env.TRANSLATIONS_API_URL);
            const data = await response.json();
            i18n.translations = {
                en: data.en,
                th: data.th,
            };
        } catch (error) {
            console.error("Failed to load translations:", error);
            i18n.translations = {
                en: languageFile.en,
                th: languageFile.th,
            };
        }
    };

    const loadLanguagePreference = async () => {
        try {
            const storedLanguage = await AsyncStorage.getItem("preferredLanguage");
            if (storedLanguage) {
                setCurrentLanguage(storedLanguage == "th" ? "ไทย" : "English");
                setLanguage(storedLanguage);
            } else {
                // Only use system language if no preference is set
                const systemLanguage =
                    getLocales()[0].languageCode == "th" ? "th" : "en";
                await AsyncStorage.setItem("preferredLanguage", systemLanguage);
                setCurrentLanguage(systemLanguage == "th" ? "ไทย" : "English");
                setLanguage(systemLanguage);
            }
        } catch (error) {
            console.error("Failed to load language preference:", error);
        }
    };

    useEffect(() => {
        if (process.env.MODE === "dev") {
            loadTranslations();
            loadLanguagePreference();
        } else {
            loadLanguagePreference();
            i18n.translations = {
                en: languageFile.en,
                th: languageFile.th,
            };
        }
    }, []);

    useEffect(() => {
        i18n.locale = language;
    }, [language]);

    useEffect(() => {
        const handleDeviceLanguageChange = async () => {
            if (
                getLocales()[0].languageCode !=
                (((await AsyncStorage.getItem("preferredLanguage")) &&
                    getLocales()[0].languageCode == "en") ||
                    getLocales()[0].languageCode == "th")
            ) {
                await AsyncStorage.setItem(
                    "preferredLanguage",
                    getLocales()[0].languageCode
                );
            }
        };
        handleDeviceLanguageChange();
    }, [getLocales()[0].languageCode]);

    const updateLanguage = async (code) => {
        setLanguage(code);
        await AsyncStorage.setItem("preferredLanguage", code);
        setCurrentLanguage(code === "th" ? "ไทย" : "English");
        // No userId or setUserLanguage for now
    };

    return (
        <LanguageContext.Provider
            value={{
                locale: language,
                updateLanguage,
                t: i18n,
                currentLanguage,
                setCurrentLanguage,
            }}
        >
            {children}
        </LanguageContext.Provider>
    );
};

export default LanguageContext; 