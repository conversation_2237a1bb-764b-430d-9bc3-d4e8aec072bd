import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { compose } from "@reduxjs/toolkit";

const baseUrl =
  process.env.MODE?.toLowerCase() === "prod"
    ? process.env.PUBLIC_SERVER_BASE_URL_PROD
    : process.env.PUBLIC_SERVER_BASE_URL;

const axiosInstance = axios.create({
  baseURL: baseUrl,
  headers: {
    "Content-Type": "application/json",
    "ngrok-skip-browser-warning": "true",
  },
});


axiosInstance.interceptors.request.use(async (config) => {
  const token = await AsyncStorage.getItem("accessToken");
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const status = error.response?.status;
    const originalRequest = error.config;

    // If the refresh call itself failed, don't try to refresh again
    const isRefreshCall = originalRequest?.url?.includes('/auth/refresh');
    if ((status === 401 || status === 403) && !originalRequest._retry && !isRefreshCall) {
      originalRequest._retry = true;
      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');
        if (refreshToken) {
          // Use a bare axios without interceptors to avoid recursion
          const res = await axios.post(
            `${axiosInstance.defaults.baseURL}/auth/refresh`,
            { refreshToken }
          );

          const newAccessToken = res.data.accessToken;
          await AsyncStorage.setItem('accessToken', newAccessToken);
          // Update header on the retried request
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

          // Also update default header for future requests
          axiosInstance.defaults.headers.common.Authorization = `Bearer ${newAccessToken}`;

          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        await AsyncStorage.clear();
        return Promise.reject(refreshError);
      }
    }

    // For 401/403 after failed refresh or when no refresh is attempted
    if (status === 401 || status === 403) {
      await AsyncStorage.clear();
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
