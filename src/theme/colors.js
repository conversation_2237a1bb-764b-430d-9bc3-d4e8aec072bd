// src/theme/colors.js

const colors = {
    background: {
        main: '#eaf3f7',      // General background (header/menu)
        white: '#fff',        // White backgrounds
        green:'#7FC5C6'
    },
    header: {
        background: '#F8F8F8'
    },

    text: {
        main: '#222',         // Main text
        hospital: '#6b7b8c',
        placeholder: '#bdbdbd',
        white: '#fff',
        brown: '#BFA791',
        cardText: '#4D4D4D',
        navBarGreen: '#7FC5C6',
        footerTextGreen: '#7FC5C6',
        footerTextGray: 'rgb(142, 142, 147)',
        desaturatedCyan: '#497273',
        red: '#DEA19E'
    },
    button: {
        primary: '#7FC5C6',
        red: '#ff4d4f',
        role: '#7FC5C6',
        error: '#ff4d4f',
    },
    border: {
        main: '#eee',
        red: '#ff4d4f',
        headingText: "#C8C7CC",
        green: "#7FC5C6",
        gray: '#ccc'
    },
    icon: {
        main: '#3db2ff',
        danger: '#ff4d4f',
        arrow: '#bdbdbd',
        white: '#fff',
    },
    shadow: {
        main: '#000',
    },
    card: {
        brownBackground: "#BFA791",
        redBackground: '#DEA19E'
    },
    footer: {
        background: '#F7F7F7',
        green:'#7FC5C6'
    },
    progress:{
        primary:"#7FC5C6"
    }
};

export default colors;