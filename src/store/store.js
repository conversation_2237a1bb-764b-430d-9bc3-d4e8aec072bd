import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import authReducer from '../features/auth/authSlice';
import homeReducer from '../features/home/<USER>'
import patientsReducer from '../features/patients/patientsSlice';
import registerReducer from '../features/register/registerSlice'
import sectionsReducer from '../features/sections/sectionSlice'

const rootReducer = combineReducers({
  auth: authReducer,
  home: homeReducer,
  patients: patientsReducer, // Added patients reducer
  register: registerReducer,
  sections: sectionsReducer
});

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['auth'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // needed for redux-persist
    }),
});

export const persistor = persistStore(store);