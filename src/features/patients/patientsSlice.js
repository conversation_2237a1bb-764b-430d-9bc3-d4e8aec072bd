import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axiosInstance from '../../services/axiosInstance';

// Async thunk to fetch patients
export const fetchPatients = createAsyncThunk(
    'patients/fetchPatients',
    async ({ status, limit = 10, offset = 0 }, { rejectWithValue }) => {
        try {
            // Construct query params
            const params = new URLSearchParams({
                status,
                limit: limit.toString(),
                offset: offset.toString(),
            });
            const response = await axiosInstance.get(`/patients/patients-list?${params}`);
            // Axios puts the response data in response.data
            return response.data.results || [];
            // return [];
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
);

// Async thunk to fetch patient by TNR and hospital
export const fetchPatientByTnrHospital = createAsyncThunk(
    'patients/fetchPatientByTnrHospital',
    async ({ TNR, hospital }, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post('/patients/by-tnr-hospital', {
                TNR,
                hospital
            });
            return response.data.data;
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
);

export const searchPatients = createAsyncThunk(
    'patients/searchPatients',
    async (searchTerm, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post(`/patients/search?search=${encodeURIComponent(searchTerm)}`, {}
            );
            return response.data.results || [];
        } catch (error) {
            return rejectWithValue(error.response?.data?.message || error.message);
        }
    }
);

// Thunk to search by TNR or fullname
export const fetchPatientByTnrOrName = (searchValue) => async (dispatch, getState) => {
  try {
    dispatch(setPatientLoading(true));

    const state = getState();
    const hospital = state.auth?.user?.hospital;

    if (!hospital || !searchValue) return;

    const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/patients/by-tnr-hospital`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${state.auth?.token}`, // if auth is required
      },
      body: JSON.stringify({
        TNR: searchValue,
        hospital: hospital
      }),
    });

    const data = await response.json();

    if (data.status === 1) {
      dispatch(setSinglePatient(data.data)); // <- create this action
    } else {
      dispatch(setSinglePatient(null));
    }
  } catch (error) {
    console.error('Error fetching patient by TNR:', error);
    dispatch(setSinglePatient(null));
  } finally {
    dispatch(setPatientLoading(false));
  }
};

export const fetchReferredPatients = createAsyncThunk(
  'refer/fetchReferredPatients',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const hospital = state.auth?.user?.hospital;

      const response = await axiosInstance.get('/refer/referred-patients', {
        params: { hospital }
      });

      return response.data || [];
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || error.message);
    }
  }
);

export const fetchSortedPatientsThunk = createAsyncThunk(
    'patients/fetchSorted',
    async (params, thunkAPI) => {
        try {
            const response = await axiosInstance.get('/patients/sorted', {
                params,
            });
            return response.data;
        } catch (error) {
            return thunkAPI.rejectWithValue(error.response?.data?.message || 'Failed to fetch sorted patients');
        }
    }
);

const patientsSlice = createSlice({
    name: 'patients',
    initialState: {
        data: [],
        selectedPatient: null,
        loading: false,
        loadingMore: false,
        error: null,
        offset: 0,
        hasMore: true,
        searchResults: [], // 
        searchValue:'',
        cameFromPatientDisplay: false, 
        singlePatient: null,
        patientLoading: false,
        referredPatients: [],
        referredLoading: false,
        loadingMoreSort: false,
        offsetSort: 0,
        hasMoreSort: true,
        sortError: null,
        cameFromReferList: false, 
    },
    reducers: {
        resetPatients: (state) => {
            state.data = [];
            state.offset = 0;
            state.hasMore = true;
            state.error = null;
        },
        resetSortedPatients: (state) => {
            state.loading = false;
            state.loadingMoreSort = false;
            state.data = [];
            state.offsetSort = 0;
            state.hasMoreSort = true;
            state.sortError = null;
        },
        clearSelectedPatient: (state) => {
            state.selectedPatient = null;
            state.error = null;
        },
        setSearchValue: (state, action) => {
            state.searchValue = action.payload;
        },
        setCameFromPatientDisplay: (state, action) => {
            state.cameFromPatientDisplay = action.payload;
        },
         setSinglePatient(state, action) {
        state.singlePatient = action.payload;
        },
        setPatientLoading(state, action) {
        state.patientLoading = action.payload;
        },
        setOffsetSortToZero: (state) => {
            state.offsetSort = 0;
        },
        setCameFromReferList: (state, action) => {
            state.cameFromReferList = action.payload;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchPatients.pending, (state, action) => {
                const { offset } = action.meta.arg;
                if (offset === 0) {
                    state.loading = true;
                } else {
                    state.loadingMore = true;
                }
                state.error = null;
            })
            .addCase(fetchPatients.fulfilled, (state, action) => {
                const { offset } = action.meta.arg;
                if (offset === 0) {
                    state.loading = false;
                    state.data = action.payload;
                } else {
                    state.loadingMore = false;
                    state.data = [...state.data, ...action.payload];
                }

                if (!action.payload || action.payload.length === 0) {
                    state.hasMore = false;
                } else {
                    state.offset += action.payload.length;
                }
            })
            .addCase(fetchPatients.rejected, (state, action) => {
                const { offset } = action.meta.arg;
                if (offset === 0) {
                    state.loading = false;
                } else {
                    state.loadingMore = false;
                }
                state.error = action.payload || 'Failed to fetch patients';
            })
            .addCase(fetchPatientByTnrHospital.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchPatientByTnrHospital.fulfilled, (state, action) => {
                state.loading = false;
                state.selectedPatient = action.payload;
            })
            .addCase(fetchPatientByTnrHospital.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || 'Failed to fetch patient';
            })
            .addCase(searchPatients.pending, (state) => {
                state.loading = true;
                state.searchResults = []; // Optional: clear previous
            })
            .addCase(searchPatients.fulfilled, (state, action) => {
                state.loading = false;
                state.searchResults = action.payload.data;
            })
            .addCase(searchPatients.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload || 'Search failed';
            })
            .addCase(fetchReferredPatients.pending, (state) => {
                state.referredLoading = true;
            })
            .addCase(fetchReferredPatients.fulfilled, (state, action) => {
                state.referredLoading = false;
                state.referredPatients = action.payload;
            })
            .addCase(fetchReferredPatients.rejected, (state, action) => {
                state.referredLoading = false;
                state.error = action.payload || 'Failed to fetch referred patients';
            })

            .addCase(fetchSortedPatientsThunk.pending, (state, action) => {
                if (state.offsetSort === 0) {
                    state.loading = true;
                } else {
                    state.loadingMoreSort = true;
                }
                state.sortError = null;
            })
            .addCase(fetchSortedPatientsThunk.fulfilled, (state, action) => {
                const fetchedData = action.payload?.results || action.payload?.rows || [];
                if (state.offsetSort == 0) {
                    state.loading = false;
                    state.data = fetchedData;
                    state.offsetSort = fetchedData.length;
                } else {
                    state.loadingMoreSort = false;
                    state.data = [...state.data, ...fetchedData];
                    state.offsetSort += fetchedData.length;
                }
                state.hasMoreSort = fetchedData.length > 0;
            })
            .addCase(fetchSortedPatientsThunk.rejected, (state, action) => {
                if (state.offsetSort === 0) {
                    state.loading = false;
                } else {
                    state.loadingMoreSort = false;
                }

                state.sortError = action.payload || 'Failed to fetch sorted patients';
            });
    },
});

export const { resetPatients, clearSelectedPatient, setSearchValue,
    setCameFromPatientDisplay, resetSortedPatients, setOffsetSortToZero, setCameFromReferList } = patientsSlice.actions;
export default patientsSlice.reducer; 
