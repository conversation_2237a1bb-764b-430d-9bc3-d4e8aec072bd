import CompleteIcon from '../../assets/img/complete.svg';
import FailLoginIcon from '../../assets/img/faillogin.svg';

export function getValidationStatus(pna, symptoms, t) {
    // PNA is numeric (28 for <=28 days, 29 for >28 days)
    const pnaNum = Number(pna);
    const isPnaValid = Number.isFinite(pnaNum) && pnaNum <= 28;
    const isSymptomsValid = Array.isArray(symptoms) && symptoms.length > 0;

    const isValid = isPnaValid && isSymptomsValid;

    const svg = isValid
        ? <CompleteIcon width={107} height={107} />
        : <FailLoginIcon width={120} height={120} />;

    const screenConfig = {
        backgroundColor: isValid ? '#73b8b8' : '#d28a8a',
        title: t ? (isValid ? t.t('Meets Criteria') : t.t('Does Not Meet Criteria')) : (isValid ? 'ตรงตามเงื่อนไข' : 'ไม่ตรงตามเงื่อนไข'),
        description: t ? (isValid ? t.t('Patient meets criteria for recording data in Thai National NewBorn Registry system') : t.t('Patient does not meet criteria for recording data in Thai National NewBorn Registry system')) : (isValid ? 'คนไข้ตรงตามเงื่อนไขในการบันทึกข้อมูลลงระบบ Thai National NewBorn Registry' : 'คนไข้ไม่ตรงตามเงื่อนไขในการบันทึกข้อมูลลงระบบ Thai National NewBorn Registry'),
        buttonText: t ? (isValid ? t.t('Start recording data for registration') : t.t('End validation')) : (isValid ? 'เริ่มการบันทึกข้อมูลลงทะเบียน' : 'สิ้นสุดการตรวจสอบ'),
        svg
    };

    return { isValid, screenConfig };
}
