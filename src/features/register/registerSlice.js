import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axiosInstance from '../../services/axiosInstance';



// Fetch hospitals list
export const fetchHospitalsThunk = createAsyncThunk(
    'register/fetchHospitals',
    async ({ excludeSelf = false, exclude_hospital_name } = {}, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.get('/hospitals', {
                params: {
                    excludeSelf,
                    exclude_hospital_name,
                },
            });
            return res.data; // expecting { status: 1, data: hospitals }
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);

export const generateTNRThunk = createAsyncThunk(
    'register/generateTNR',
    async (regist_date, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.post('/patients/generate-tnr', { regist_date });
            return res.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);

// Thunk for creating register criteria
export const createRegisterCriteriaThunk = createAsyncThunk(
    'register/createRegisterCriteria',
    async (_, { getState, rejectWithValue }) => {
        try {
            const { register } = getState(); 
            const payload = {
                TNR: register.TNR || '',
                ward: register.admitWard,
                pna: register.pna,
                patient_symptoms: Array.isArray(register.symptoms) ? register.symptoms.join(',') : register.symptoms,
            };
            const response = await axiosInstance.post('/register-criteria/create', payload);
            return response.data;
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);

export const createPatientThunk = createAsyncThunk(
    'register/createPatient',
    async (patientData, { getState, rejectWithValue }) => {
        try {
            const { register } = getState();
            const TNR = register.TNR;

            if (!TNR) {
                return rejectWithValue({ message: 'TNR is required before creating patient.' });
            }

            const payload = { TNR, ...patientData };
            const res = await axiosInstance.post('/patients/create', payload);
            return res.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);


export const saveRegisterProgressThunk = createAsyncThunk(
    'register/saveRegisterProgress',
    async ({ TNR, temporary, permanent }, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.post('/patients/create-register-progress', {
                TNR,
                temporary,
                permanent,
            });
            return res.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);

// Thunk for validating infant ID
export const validateInfantIdThunk = createAsyncThunk(
    'register/validateInfantId',
    async ({ infantId, TNR }, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.post('/patients/validate-infant-id', { id: infantId, TNR });
            return res.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);

// Thunk for fetching register progress
export const fetchRegisterProgressThunk = createAsyncThunk(
    'register/fetchRegisterProgress',
    async (TNR, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.post('/patients/register-progress', { TNR });
            return res.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);


const initialState = {
    admitWard: '',
    pna: '',
    symptoms: [],
    currentStep: 0,
    completedSteps: [],
    formData: null,
    TNR: '',
    regist_date: '',
    patientData: {},
    validateInfantIdResult: "",
    registerProgress: null,
    hospitals: [],
    hospitalsLoading: false,

    loading: false,
    error: null,
};

const registerSlice = createSlice({
    name: 'register',
    initialState,
    reducers: {
        setAdmitWard: (state, action) => {
            state.admitWard = action.payload;
        },
        setTNR: (state, action) => {
            state.TNR = action.payload;
        },
        setPna: (state, action) => {
            state.pna = action.payload;
        },
        setFormData: (state, action) => {
            state.formData = action.payload;
        },
        toggleSymptom: (state, action) => {
            const symptom = action.payload;
            if (state.symptoms.includes(symptom)) {
                state.symptoms = state.symptoms.filter(item => item !== symptom);
            } else {
                state.symptoms.push(symptom);
            }
        },
        resetStep: (state, action) => {
            const step = action.payload;
            if (step === 0) state.admitWard = null;
            if (step === 1) state.pna = '';
            if (step === 2) state.symptoms = [];
        },
        resetCriteria: () => initialState,
        setCurrentStep: (state, action) => {
            state.currentStep = action.payload;
        },
        incrementStep: (state) => {
            state.currentStep += 1;
        },
        decrementStep: (state) => {
            state.currentStep = Math.max(0, state.currentStep - 1);
        },
        markStepCompleted: (state, action) => {
            const step = action.payload;
            if (!state.completedSteps.includes(step)) {
                state.completedSteps.push(step);
            }
        },
        unmarkStepCompleted: (state, action) => {
            const step = action.payload;
            state.completedSteps = state.completedSteps.filter(s => s !== step);
        },
    },

    extraReducers: (builder) => {
        builder
            // generateTNR
            .addCase(generateTNRThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(generateTNRThunk.fulfilled, (state, action) => {
                state.loading = false;
                const payload = action.payload;
                const tnr =
                    typeof payload === 'string'
                        ? payload
                        : payload?.TNR || payload?.tnr || payload?.data || '';
                state.TNR = tnr?.TNR;
            })
            .addCase(generateTNRThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to generate TNR';
            })

            // submitRegisterCriteria
            .addCase(createRegisterCriteriaThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createRegisterCriteriaThunk.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(createRegisterCriteriaThunk.rejected, (state, action) => {
                state.loading = false;
                state.error =
                    action.payload?.message || 'Failed to submit register criteria';
            })

            // createPatient
            .addCase(createPatientThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(createPatientThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.patientData = action.payload;
            })
            .addCase(createPatientThunk.rejected, (state, action) => {
                state.loading = false;
                state.error =
                    action.payload?.message || 'Failed to create patient';
            })

            .addCase(saveRegisterProgressThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(saveRegisterProgressThunk.fulfilled, (state, action) => {
                state.loading = false;
                // Optionally store response data if needed
                // state.progressData = action.payload.data;
            })
            .addCase(saveRegisterProgressThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to save register progress';
            })

            .addCase(validateInfantIdThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(validateInfantIdThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.validateInfantIdResult = action.payload;
            })
            .addCase(validateInfantIdThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to validate infant ID';
            })

            .addCase(fetchRegisterProgressThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchRegisterProgressThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.registerProgress = action.payload.data;
            })
            .addCase(fetchRegisterProgressThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to fetch register progress';
            })
            
            // fetchHospitals
            .addCase(fetchHospitalsThunk.pending, (state) => {
                state.hospitalsLoading = true;
                state.error = null;
            })
            .addCase(fetchHospitalsThunk.fulfilled, (state, action) => {
                state.hospitalsLoading = false;
                state.hospitals = action.payload?.data || [];
            })
            .addCase(fetchHospitalsThunk.rejected, (state, action) => {
                state.hospitalsLoading = false;
                state.error = action.payload?.message || 'Failed to fetch hospitals';
            });
    },
});

export const { setAdmitWard, setPna, toggleSymptom, resetStep, resetCriteria, setCurrentStep, incrementStep, decrementStep, markStepCompleted, unmarkStepCompleted, setFormData,setTNR} = registerSlice.actions;
export default registerSlice.reducer;
