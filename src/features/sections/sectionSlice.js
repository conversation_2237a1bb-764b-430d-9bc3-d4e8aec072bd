import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axiosInstance from '../../services/axiosInstance';

// Thunk for saving section 1 data
export const saveSectionThunk = createAsyncThunk(
    'section/saveSection',
    async ({num, formData }, { rejectWithValue }) => {
      try {
        const response = await axiosInstance.post('/sections/save', {
          num,           // take from payload
          data: formData,
        });
        return response.data;
    } catch (e) {
        return rejectWithValue(e.response?.data || { message: e.message });
      }
    }
  );
  

// Thunk for fetching section progress
export const fetchSectionProgressThunk = createAsyncThunk(
    'section/fetchSectionProgress',
    async ({ TNR, hospital }, { rejectWithValue }) => {
        try {
            const res = await axiosInstance.post('/patients/section-progress', { TNR, hospital });
            return res.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);

// Thunk for updating section progress
export const updateSectionProgressThunk = createAsyncThunk(
    'section/updateSectionProgress',
    async ({ TNR, num }, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.post('/sections/progress', {
                num,
                TNR,
                status: 'done'
            });
            return response.data;
        } catch (e) {
            return rejectWithValue(e.response?.data || { message: e.message });
        }
    }
);

const initialState = {
    sectionProgress: null,
    saveStatus: 'idle', // 'idle' | 'loading' | 'succeeded' | 'failed'
    saveError: null,
    loading: false,
    error: null,
};

const sectionSlice = createSlice({
    name: 'sections',
    initialState,
    reducers: {
        clearSectionProgress: (state) => {
            state.sectionProgress = null;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        // Handle updateSectionProgressThunk
        builder
            .addCase(updateSectionProgressThunk.pending, (state) => {
                state.saveStatus = 'loading';
                state.saveError = null;
            })
            .addCase(updateSectionProgressThunk.fulfilled, (state) => {
                state.saveStatus = 'succeeded';
            })
            .addCase(updateSectionProgressThunk.rejected, (state, action) => {
                state.saveStatus = 'failed';
                state.saveError = action.payload?.message || 'Failed to update section progress';
            })
            .addCase(saveSectionThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(saveSectionThunk.fulfilled, (state) => {
                state.loading = false;
            })
            .addCase(saveSectionThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to save section';
            })
            .addCase(fetchSectionProgressThunk.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchSectionProgressThunk.fulfilled, (state, action) => {
                state.loading = false;
                state.sectionProgress = action.payload.data;
            })
            .addCase(fetchSectionProgressThunk.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload?.message || 'Failed to fetch section progress';
            });
    },
});

export const { clearSectionProgress } = sectionSlice.actions;
export default sectionSlice.reducer;