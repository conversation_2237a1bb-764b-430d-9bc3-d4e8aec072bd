import { z } from 'zod';

export const Section1Schema = z.object({
    admission_type: z
        .enum(['inborn', 'outborn', 'bba'])
        .nullable()
        .refine((val) => val !== null, {
            message: 'Please select a valid admission type',
        }),

    inborn_transfer_type: z.enum(['intrauterine', 'not_intrauterine']).nullable().optional(),
    outborn_transfer_type: z.enum(['tnr', 'non_tnr']).nullable().optional(),

    tnr_hospital_value: z.string().optional(),
    tnr_hospital_name: z.string().optional(),
    tnr_hospital_code: z.string().optional(),
    non_tnr_hospital_name: z.string().optional(),

    discharge_tnr_hospital_value: z.string().optional(),
    discharge_tnr_hospital_name: z.string().optional(),
    discharge_tnr_hospital_code: z.string().optional(),
    discharge_level2_hospital_name: z.string().optional(),

    admitted_to: z.string().min(1, { message: "Please select a ward" }),
    admission_date: z.string().min(1, { message: "Admission date is required" }),
    discharge_date: z.string().min(1, { message: "Discharge date is required" }),

    discharge_status: z.string({
        required_error: "Discharge status is required"
    }).refine(
        (val) => ['refer_tnr', 'discharge_home', 'refer_level2', 'death'].includes(val),
        { message: "Please select a valid discharge status" }
    ),

    discharge_tnr_hospital: z.string().optional(),
    discharge_level2_hospital: z.string().optional(),
    hospital_stay: z.string().optional(),
}).superRefine((data, ctx) => {
    if (data.admission_type === 'inborn' && !data.inborn_transfer_type) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Intrauterine transfer status is required.',
            path: ['inborn_transfer_type'],
        });
    }
    if (data.admission_type === 'outborn') {
        if (!data.outborn_transfer_type) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Hospital transfer type is required.',
                path: ['outborn_transfer_type'],
            });
        } else {
            if (
                data.outborn_transfer_type === 'tnr' &&
                (data.tnr_hospital_name === undefined || data.tnr_hospital_name === null) &&
                (data.tnr_hospital_code === undefined || data.tnr_hospital_code === null)
            ) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Please select a valid discharge hospital.',
                    path: ['tnr_hospital_name'],
                });
            }

            if (data.outborn_transfer_type === 'non_tnr' && (!data.non_tnr_hospital_name || data.non_tnr_hospital_name.trim() === '')) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Non-TNR member hospital name is required.',
                    path: ['non_tnr_hospital_name'],
                });
            }
        }
    }
    if (
        data.discharge_status === 'refer_tnr' &&
        (data.discharge_tnr_hospital === undefined || data.discharge_tnr_hospital === null) &&
        (data.discharge_tnr_hospital_code === undefined || data.discharge_tnr_hospital_code === null)
    ) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Please select a valid discharge hospital.',
            path: ['discharge_tnr_hospital'],
        });
    }


    if (data.discharge_status === 'refer_level2' && (!data.discharge_level2_hospital || data.discharge_level2_hospital.trim() === '')) {
        ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Level 2/Non-TNR hospital name is required.',
            path: ['discharge_level2_hospital'],
        });
    }

    // Validate that discharge date is greater than or equal to admission date
    if (data.admission_date && data.discharge_date) {
        const admissionDate = new Date(data.admission_date);
        const dischargeDate = new Date(data.discharge_date);

        if (dischargeDate < admissionDate) {
            ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: 'Discharge date must be greater than or equal to admission date.',
                path: ['discharge_date'],
            });
        }
    }
});

export const section1DefaultValues = {
    admission_type: null,  // Empty string for required field
    inborn_transfer_type: undefined,
    outborn_transfer_type: undefined,
    tnr_hospital_name: '',
    non_tnr_hospital_name: '',
    admitted_to: "",
    admission_date: '',
    discharge_date: '',
    discharge_status: "",
    discharge_hospital: '',
    discharge_level2_hospital: '',
    hospital_stay: '',
};
