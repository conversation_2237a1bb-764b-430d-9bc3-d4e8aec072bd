import { z } from "zod";

export const section4Schema = z
  .object({
    // Date of birth fields
    birth_day: z.string().optional(),
    birth_month: z.string().optional(),
    birth_year: z.string().optional(),
    birth_date: z.string().min(1, { message: "Date of birth is required" }),

    // Time of birth fields
    birth_hour: z
      .string()
      .min(1, { message: "Hour is required" })
      .refine((val) => {
        const hour = parseInt(val, 10);
        return !isNaN(hour) && hour >= 0 && hour <= 23;
      }, { message: "Hour must be between 0 and 23" }),

    birth_minute: z
      .string()
      .min(1, { message: "Minute is required" })
      .refine((val) => {
        const minute = parseInt(val, 10);
        return !isNaN(minute) && minute >= 0 && minute <= 59;
      }, { message: "Minute must be between 0 and 59" }),

    // Gestational age
    gestational_weeks: z
      .string()
      .min(1, { message: "Gestational weeks is required" })
      .refine((val) => {
        const weeks = parseInt(val, 10);
        return !isNaN(weeks) && weeks >= 20 && weeks <= 45;
      }, { message: "Gestational weeks must be between 20 and 45" }),

    gestational_days: z
      .string()
      .min(1, { message: "Gestational days is required" })
      .refine((val) => {
        const days = parseInt(val, 10);
        return !isNaN(days) && days >= 0 && days <= 6;
      }, { message: "Gestational days must be between 0 and 6" }),

    // Birth weight
    birth_weight: z
      .string()
      .min(1, { message: "Birth weight is required" })
      .refine((val) => {
        const weight = parseFloat(val);
        return !isNaN(weight) && weight >= 250 && weight <= 25000;
      }, { message: "Birth weight must be between 250 and 25000 grams" }),

    // Length and head circumference with N/A options
    length: z.string().optional(),
    length_na: z.boolean().default(false),
    head_circumference: z.string().optional(),
    head_circumference_na: z.boolean().default(false),

    // Gender
    gender: z.string().nullable().refine((val) => {
      return val !== null && ["male", "female", "ambiguous"].includes(val);
    }, { message: "Gender is required" }),

    // Growth status
    growth_status: z.string().nullable().refine((val) => {
      return val !== null && ["sga", "aga", "lga"].includes(val);
    }, { message: "Growth status is required" }),
  })
  .superRefine((data, ctx) => {
    // ✅ Length check
    if (!data.length_na) {
      if (!data.length || data.length.trim() === "") {
        ctx.addIssue({
          path: ["length"],
          message: "Length is required - either enter a value or select N/A",
          code: z.ZodIssueCode.custom,
        });
      } else {
        const len = parseFloat(data.length);
        if (isNaN(len) || len < 10 || len > 100) {
          ctx.addIssue({
            path: ["length"],
            message: "Length must be between 10 and 100 cm",
            code: z.ZodIssueCode.custom,
          });
        }
      }
    }

    // ✅ Head circumference check
    if (!data.head_circumference_na) {
      if (!data.head_circumference || data.head_circumference.trim() === "") {
        ctx.addIssue({
          path: ["head_circumference"],
          message:
            "Head circumference is required - either enter a value or select N/A",
          code: z.ZodIssueCode.custom,
        });
      } else {
        const hc = parseFloat(data.head_circumference);
        if (isNaN(hc) || hc < 15 || hc > 70) {
          ctx.addIssue({
            path: ["head_circumference"],
            message: "Head circumference must be between 15 and 70 cm",
            code: z.ZodIssueCode.custom,
          });
        }
      }
    }
  });

export const section4DefaultValues = {
  birth_day: "",
  birth_month: "",
  birth_year: "",
  birth_date: "",
  birth_hour: "",
  birth_minute: "",
  gestational_weeks: "",
  gestational_days: "",
  birth_weight: "",
  length: "",
  length_na: false,
  head_circumference: "",
  head_circumference_na: false,
  gender: null,
  growth_status: null,
};
