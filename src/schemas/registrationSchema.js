import { z } from 'zod';

const phoneRegex = /^0[689]\d{8}$/;

export const RegistrationSchema = z
  .object({
    infantName: z.string().min(1, 'Infant Name is required'),
    hospital: z.string().optional(),
    hn: z.string().min(1, 'Hospital Number is required'), // required
    hnOptional: z.string().optional(),
    infantId: z.string().optional(),
    permanentInfantId: z.string().optional(),
    dob: z.string().optional(),
    sex: z.string().optional(),
    ethnic: z.string().optional(),
    motherName: z.string().optional(),
    idCard: z.string().optional(),
    passport: z.string().optional(),
    hasPermanentId: z.string({
      required_error: 'Please select either Has Permanent ID or No Permanent ID',
    }).refine(val => val === 'yes' || val === 'no', {
      message: 'Please select either Has Permanent ID or No Permanent ID',
    }),
    address: z.string().optional(),
    telephone: z
      .union([z.string().regex(phoneRegex, 'Invalid Thai phone number'), z.literal('')])
      .optional(),
    contactName: z.string().optional(),
    contactRelationship: z.string().optional(),
    contactPhone: z
      .union([z.string().regex(phoneRegex, 'Invalid Thai phone number'), z.literal('')])
      .optional(),
    otherContact: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.hasPermanentId === 'yes') {
      const hasPermanentId = data.permanentInfantId && data.permanentInfantId.trim() !== '';
      const hasPassport = data.passport && data.passport.trim() !== '';
      
      // If neither ID nor passport is provided
      if (!hasPermanentId && !hasPassport) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['permanentInfantId'],
          message: 'Provide 13-digit ID number or a passport number',
        });
      }
      
      // If permanent ID is provided, validate it's exactly 13 digits
      if (hasPermanentId && data.permanentInfantId.trim().length !== 13) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['permanentInfantId'],
          message: 'Permanent ID must be exactly 13 digits',
        });
      }
      
      // Basic passport validation (at least 6 characters, alphanumeric)
      if (hasPassport && !/^[a-zA-Z0-9]{6,}$/.test(data.passport.trim())) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['passport'],
          message: 'Please enter a valid passport number',
        });
      }
    } else if(data.hasPermanentId === 'no') {
      const hospitalProvided = data.hospital && data.hospital.trim() !== '';
      const hnProvided = data.hnOptional && data.hnOptional.trim() !== '';
      if (!hospitalProvided) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['hospital'],
          message: 'Please select a hospital',
        });
      }
      if (!hnProvided) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['hnOptional'],
          message: 'Please enter Hospital Number (HN)',
        });
      }
    }
  })

export const registrationDefaultValues = {
    infantName: "",
    hospital: "",
    hn: "",
    infantId: "",
    permanentInfantId: "",
    dob: "",
    sex: "",
    ethnic: "",
    motherName: "",
    idCard: "",
    passport: "",
    hasPermanentId: "",
    address: "",
    telephone: "",
    contactName: "",
    contactRelationship: "",
    contactPhone: "",
    otherContact: "",
};
