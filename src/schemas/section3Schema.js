import { z } from 'zod';

export const section3Schema = z.object({
  // Mode of delivery
  mode_of_delivery: z.enum(['vaginal', 'caesarean_section'])
    .nullable()
    .refine((val) => val !== null, {
      message: "Mode of delivery is required",
    }),

  vaginal_type: z.enum(['with_instrument', 'without_instrument']).nullable().optional(),

  caesarean_types: z.array(z.string()).default([]),
  caesarean_other_specify: z.string().optional(),

  // Apgar scores
  apgar_1min: z.string().optional(),
  apgar_5min: z.string().optional(),
  apgar_10min: z.string().optional(),
  apgar_1min_na: z.boolean().default(false),
  apgar_5min_na: z.boolean().default(false),
  apgar_10min_na: z.boolean().default(false),

  // Resuscitation
  resuscitation: z.enum(['no', 'yes', 'na'])
    .nullable()
    .refine((val) => val !== null, {
      message: "Resuscitation field is required",
    }),

  resuscitation_cpap: z.boolean().default(false),
  resuscitation_ppv: z.boolean().default(false),
  resuscitation_intubation: z.boolean().default(false),
  resuscitation_chest_compression: z.boolean().default(false),
  resuscitation_epinephrine: z.boolean().default(false),

  // Cord blood gas
  cord_blood_gas: z.enum(['not_done', 'available', 'na'])
    .nullable()
    .refine((val) => val !== null, {
      message: "Cord blood gas field is required",
    }),

  cord_blood_ph: z.string().optional(),
  cord_blood_pco2: z.string().optional(),
  cord_blood_hco2: z.string().optional(),
  cord_blood_be: z.string().optional(),

  // Delayed cord clamping
  delayed_cord_clamping: z.enum(['no', 'yes', 'na'])
    .nullable()
    .refine((val) => val !== null, {
      message: "Delayed cord clamping field is required",
    }),
}).superRefine((data, ctx) => {
  // 1. Vaginal type check
  if (data.mode_of_delivery === 'vaginal' && !data.vaginal_type) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Please select vaginal delivery type (with or without instrument)",
      path: ["vaginal_type"],
    });
  }

  // 2. Caesarean types check
  if (
    data.mode_of_delivery === 'caesarean_section' &&
    (!data.caesarean_types || data.caesarean_types.length === 0)
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Please select at least one caesarean section type",
      path: ["caesarean_types"],
    });
  }

  // 3. Caesarean "other" check
  if (
    data.mode_of_delivery === 'caesarean_section' &&
    data.caesarean_types.includes('other') &&
    (!data.caesarean_other_specify || data.caesarean_other_specify.trim() === '')
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Please specify the other caesarean type",
      path: ["caesarean_other_specify"],
    });
  }

  // 2. Apgar score checks
  const apgarFields = [
    { score: 'apgar_1min', na: 'apgar_1min_na' },
    { score: 'apgar_5min', na: 'apgar_5min_na' },
    { score: 'apgar_10min', na: 'apgar_10min_na' }
  ];

  let hasApgarError = false;
  for (const field of apgarFields) {
    const hasScore = data[field.score] && data[field.score].trim() !== '';
    const isNA = data[field.na];

    if (!hasScore && !isNA) {
      hasApgarError = true;
      break;
    }
  }

  if (hasApgarError) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "For each Apgar score, either fill the score or select N/A",
      path: ["apgar_1min"],
    });
  }

  // 3. Resuscitation methods if resuscitation === "yes"
  if (data.resuscitation === 'yes') {
    const methods = [
      data.resuscitation_cpap,
      data.resuscitation_ppv,
      data.resuscitation_intubation,
      data.resuscitation_chest_compression,
      data.resuscitation_epinephrine
    ];

    if (!methods.some(Boolean)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "If resuscitation is yes, please select at least one resuscitation method",
        path: ["resuscitation"],
      });
    }
  }

  // 4. Cord blood gas check
  if (
    data.cord_blood_gas === 'available' &&
    (!data.cord_blood_ph || data.cord_blood_ph.trim() === '')
  ) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "pH is required",
      path: ["cord_blood_ph"],
    });
  }
});



export const section3DefaultValues = {
  mode_of_delivery: null,
  vaginal_type: null,
  caesarean_types: [],
  caesarean_other_specify: '',
  apgar_1min: '',
  apgar_5min: '',
  apgar_10min: '',
  apgar_1min_na: false,
  apgar_5min_na: false,
  apgar_10min_na: false,
  resuscitation: null,
  resuscitation_cpap: false,
  resuscitation_ppv: false,
  resuscitation_intubation: false,
  resuscitation_chest_compression: false,
  resuscitation_epinephrine: false,
  cord_blood_gas: null,
  cord_blood_ph: '',
  cord_blood_pco2: '',
  cord_blood_hco2: '',
  cord_blood_be: '',
  delayed_cord_clamping: null,
};