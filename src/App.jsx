import React from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './store/store';
import Login from './screens/Login/Login';
import { LanguageProvider } from './context/LanguageContext';
import { ExpoRoot } from 'expo-router';
import { useAppFonts } from './theme/fontLoader';
import { ActivityIndicator, View } from 'react-native';
import { PaperProvider } from 'react-native-paper';

export default function App() {
  const fontsLoaded = useAppFonts();
  const ctx = require.context("./app")
  if (!fontsLoaded) return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" color="#7FC5C6" />
    </View>
  );
  return (
    <LanguageProvider >
      <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
          <PaperProvider>
            <ExpoRoot context={ctx} />
            {/* <Login /> */}
          </PaperProvider>
          </PersistGate>
      </Provider>
    </LanguageProvider>
  );
} 