// Centralized route path config for file-based routing
// Organized into public and private routes

const ROUTES = {
    PUBLIC: {
        LOGIN: '/publicRoutes/login',
        FORGOT_PASSWORD: '/publicRoutes/forgotPassword',
    },
    PRIVATE: {
        HOME: '/privateRoutes/home',
        PATIENT_LIST: '/privateRoutes/patientList',
        REGISTRATION: '/privateRoutes/registration',
        SEARCH_TN_NUMBER: '/privateRoutes/SearchTNnumber',
        REGISTER_CRITERIA: '/privateRoutes/register-criteria',
        VALIDATE_REGISTER_CRITERIA: '/privateRoutes/validate-register-criteria',
        CONFIRM_REGISTRATION_SCREEN: '/privateRoutes/confirm-register-screen',
        PATIENT_DISPLAY_SCREEN: '/privateRoutes/patient-display',
        SETTINGS: '/privateRoutes/settings',
        REFER_LIST:'/privateRoutes/refer-list',
        SECTION1_FORM: '/privateRoutes/section1',
        SECTION2_FORM:'/privateRoutes/section2',
        SECTION3_FORM:'/privateRoutes/section3',
        SECTION4_FORM:'/privateRoutes/section4',
    }
};

export default ROUTES;
