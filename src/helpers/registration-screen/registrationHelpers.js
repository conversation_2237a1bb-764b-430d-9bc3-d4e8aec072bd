export const mapPatientDataToForm = (patientData) => {
    return {
        infantName: patientData.fullname || '',
        hn: patientData.HN || '',
        infantId: patientData.infant_id?.replace('ε', '') || '',
        dob: patientData.DOB || '',
        sex: patientData.sex || '',
        ethnic: patientData.ethnic || '',
        motherName: patientData.mother_fullname || '',
        idCard: patientData.mother_id || '',
        passport: patientData.mother_passport || '',
        address: patientData.mother_address || '',
        telephone: patientData.mother_tel || '',
        contactName: patientData.contact_person_name || '',
        contactRelationship: patientData.relation || '',
        contactPhone: patientData.contact_person_tel || '',
        otherContact: patientData.other_contact || '',
    };
}

export const generateInfantId = (hospitalNumber = '') => {
    // Format: <hospitalNumber>-YYYYMMDD-SSS
    const hnPart = String(hospitalNumber || '').trim();
    const d = new Date();
    const y = d.getFullYear();
    const m = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const seq = String(d.getTime()).slice(-3);
    return `${hnPart}-${y}${m}${day}-${seq}`;
};

// Helper to filter out empty values
const clean = obj => {
    const result = {};
    Object.entries(obj).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
            result[key] = value;
        }
    });
    return result;
};

export const mapFormDataToPatient = (formData, isPermanent) => {
    const isPermanentFlag = isPermanent?.toLowerCase() === 'yes';
    const infantIdSource = isPermanent ? formData.permanentInfantId : formData.infantId;
    const infant_id = infantIdSource ? `${infantIdSource}ε` : undefined;
    const base = clean({
        fullname: formData.infantName,
        HN: isPermanentFlag ? undefined : formData.hn,
        infant_id,
        passport_id: formData.passport,
        illegal: 'No',
        DOB: formData.dob,
        sex: formData.sex,
        ethnic: formData.ethnic,
        mother_fullname: formData.motherName,
        mother_id: formData.idCard,
        mother_passport: formData.passport,
        mother_address: formData.address,
        mother_tel: formData.telephone,
        contact_person_name: formData.contactName,
        relation: formData.contactRelationship,
        contact_person_tel: formData.contactPhone,
        other_contact: formData.otherContact,
        status: 'active',
        isPermanent,
    });

    return clean({
        ...base,
        ...(!isPermanentFlag && formData.hospital ? { hospital: formData.hospital } : {})
    });
};

export const generateCurrentDate = () => {
    const dateObj = new Date();
    const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getUTCDate()).padStart(2, '0');
    const year = dateObj.getUTCFullYear();
    return `${year}-${month}-${day}`;
};