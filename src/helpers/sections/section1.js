export const toDateTime = (dateStr) => {
  if (!dateStr) return '';
  if (/(\d{4}-\d{2}-\d{2})\s+\d{2}:\d{2}:\d{2}/.test(dateStr)) return dateStr;
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return `${dateStr} 00:00:00`;
  try {
    const d = new Date(dateStr);
    const pad = (n) => `${n}`.padStart(2, '0');
    const yyyy = d.getFullYear();
    const mm = pad(d.getMonth() + 1);
    const dd = pad(d.getDate());
    const hh = pad(d.getHours());
    const mi = pad(d.getMinutes());
    const ss = pad(d.getSeconds());
    return `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`;
  } catch {
    return '';
  }
};

// Utility to strip empty/null/undefined fields
const cleanObject = (obj) => {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, v]) => v !== '' && v !== null && v !== undefined)
  );
};

export const mapSection1ToPayload = (form, ctx = {}) => {
  const {
    currentHospital = '',
    TNRPatient = ''
  } = ctx;

  let data = {
    TNR: TNRPatient,
    hospital: currentHospital || 'vernity',
    admission_status: 'admitted',
    admitted_to: form?.admitted_to,
    admission_date: toDateTime(form?.admission_date)?.split(' ')[0],
    discharge_date: toDateTime(form?.discharge_date)?.split(' ')[0],
    stay_day: form?.hospital_stay ? String(form.hospital_stay) : undefined,
  };

  // Admission type (inborn/outborn)
  if (form?.admission_type === 'inborn') {
    data.admission_status = 'inborn';
    data.intrauterine_transter = form?.inborn_transfer_type;
  } else if (form?.admission_type === 'outborn') {
    data.admission_status = 'outborn';
    if (form?.outborn_transfer_type === 'tnr') {
      data.transfer_member = 'TNR member hospital';
      data.hospital_name = form?.tnr_hospital_name;
    } else if (form?.outborn_transfer_type === 'non_tnr') {
      data.transfer_member = 'Non TNR member hospital';
      data.non_hospital_name = form?.non_tnr_hospital_name;
    }
  } else if (form?.outborn_transfer_type === 'bba') {
    data.admission_status = 'BBA';
  }

  // Discharge type
  if (form?.discharge_status === 'discharge_home') {
    data.discharge_type = 'home';
  } else if (form?.discharge_status === 'death') {
    data.discharge_type = 'death';
  } else if (form?.discharge_status === 'refer_tnr') {
    data.discharge_type = 'refer_tnr';
    data.discharge_hospital = form?.discharge_tnr_hospital;
    data.discharge_hospital_code = form?.discharge_tnr_hospital_code;
  } else if (form?.discharge_status === 'refer_level2') {
    data.discharge_type = 'transfer';
    data.discharge_level2_hospital = form?.discharge_level2_hospital;
  }

  // Remove empty values
  data = cleanObject(data);
  console.log("data",data)
  return {
    num: '1',
    data
  };
};
