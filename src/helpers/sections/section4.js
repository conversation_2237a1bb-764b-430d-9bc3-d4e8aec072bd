export const mapSection4Data = (formData, name, TNR, hospital) => {
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19);
    const username = name || 'system';

    // Map growth status to uppercase format
    const mapGrowthStatus = (status) => {
        if (!status) return '';
        const map = { sga: 'SGA', aga: 'AGA', lga: 'LGA' };
        return map[status] || '';
    };

    // Format DOB (YYYY-MM-DD)
    const formatDOB = (birthDate) => birthDate || '';

    // Format TOB (HH:MM:SS)
    const formatTOB = (hour, minute) => {
        if (!hour || !minute) return '';
        return `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}:00`;
    };

    // Build data object
    let data = {
        TNR: TNR || '', // 👈 moved inside data
        DOB: formatDOB(formData.birth_date),
        TOB: formatTOB(formData.birth_hour, formData.birth_minute),
        gender: formData.gender,
        gestational_age_week: formData.gestational_weeks,
        gestational_age_day: formData.gestational_days,
        birth_weight: formData.birth_weight,
        growth_status: mapGrowthStatus(formData.growth_status),
        last_modified_username: username,
        last_modified_date: now,
    };

    // Handle length
    if (formData.length_na) {
        data.length_NA = 'yes';
    } else if (formData.length) {
        data.length = formData.length;
        data.length_NA = 'no';
    }

    // Handle head circumference
    if (formData.head_circumference_na) {
        data.head_circumference_NA = 'yes';
    } else if (formData.head_circumference) {
        data.head_circumference = formData.head_circumference;
        data.head_circumference_NA = 'no';
    }

    // Remove empty/null/undefined values from data
    Object.keys(data).forEach((key) => {
        if (data[key] === '' || data[key] === null || data[key] === undefined) {
            delete data[key];
        }
    });

    return {
        num: '4',
        data,
    };
};


export default mapSection4Data;
