export const mapSection2Data = (formData, name, TNR, hospital) => {
    const now = new Date().toISOString();
    const username = name || 'system';

    // Helper function to map array of selected options to yes/no object
    const mapOptionsToYesNo = (selectedOptions, allOptions) => {
        return allOptions.reduce((acc, option) => {
            acc[option] = selectedOptions.includes(option) ? 'yes' : 'no';
            return acc;
        }, {});
    };

    // Map GBS screening
    const gbsOptions = ['not_done', 'positive', 'negative'];
    const gbsScreening = mapOptionsToYesNo(formData.gbs_screening || [], gbsOptions);
    
    // If 'other' is selected in GBS, add the other value
    if (formData.gbs_screening?.includes('other') && formData.gbs_screening_other) {
        gbsScreening.other = formData.gbs_screening_other;
    }

    // Map abnormal serology
    const abnormalSerologyOptions = ['normal_all', 'other', 'NA'];
    const abnormalSerology = mapOptionsToYesNo(formData.abnormal_serology || [], abnormalSerologyOptions);
    
    if (formData.abnormal_serology_other) {
        abnormalSerology.other = formData.abnormal_serology_other;
    }

    // Map complications during pregnancy
    const pregnancyComplications = {
        meternal_thyroid_disease: formData.complications_pregnancy?.includes('maternal_thyroid_disease') ? 'yes' : 'no',
        multiple_gestation: formData.complications_pregnancy?.includes('multiple_gestation') ? 'yes' : 'no',
        meternal_UTI: formData.complications_pregnancy?.includes('maternal_uti') ? 'yes' : 'no',
        meternal_drug_abuse: formData.complications_pregnancy?.includes('maternal_drug_abuse') ? 'yes' : 'no',
        no_complication: formData.complications_pregnancy?.length === 0 ? 'yes' : 'no',
        status: 'save',
        last_modified_username: username,
        last_modified_date: now
    };

    // Map intrapartum complications
    const intrapartumComplications = {
        abnormal_vaginal_bleeding: formData.intrapartum_complications?.includes('abnormal_vaginal_bleeding') ? 'yes' : 'no',
        MSAF: formData.intrapartum_complications?.includes('msaf') ? 'yes' : 'no',
        Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement: 
            formData.intrapartum_complications?.includes('fetal_distress') ? 'yes' : 'no',
        meternal_fever_or_Chrioaminonitis: 
            formData.intrapartum_complications?.includes('maternal_fever') ? 'yes' : 'no',
        prolonged_rupture_of_membrane_more_than_18_hr: 
            formData.intrapartum_complications?.includes('prom') ? 'yes' : 'no',
        prolapsed_cord: formData.intrapartum_complications?.includes('prolapsed_cord') ? 'yes' : 'no',
        IUGR: formData.intrapartum_complications?.includes('iugr') ? 'yes' : 'no',
        no_complication: formData.intrapartum_complications?.length === 0 ? 'yes' : 'no',
        status: 'save',
        last_modified_username: username,
        last_modified_date: now
    };

    // Add other intrapartum complications if any
    if (formData.intrapartum_complications_other) {
        intrapartumComplications.other = formData.intrapartum_complications_other;
    }

    // Map maternal medication
    const maternalMedication = {
        antibiotics: formData.maternal_medication?.includes('antibiotics') ? 'yes' : 'no',
        dexamamthasone_or_prenatal_steroid: 
            formData.maternal_medication?.includes('dexamethasone_prenatal_steroid') ? 'yes' : 'no',
        no_complication: formData.maternal_medication?.length === 0 ? 'yes' : 'no',
        status: 'save',
        last_modified_username: username,
        last_modified_date: now
    };

    // Add other medications if any
    if (formData.maternal_medication_other?.length > 0) {
        maternalMedication.other = formData.maternal_medication_other.join(', ');
    }

    // Construct the final payload
    const payload = {
        data: {
            TNR: TNR,
            age: formData.maternal_age || '',
            G: formData.gpa_g || '',
            P: formData.gpa_p || '',
            A: formData.gpa_a || '',
            status: 'save',
            last_modified_username: username,
            last_modified_date: now,
            abnormal_serology: {
                ...abnormalSerology,
                status: 'save',
                last_modified_username: username,
                last_modified_date: now
              },
            complication_during_pregnancy: pregnancyComplications,
            intrapartum_complication: intrapartumComplications,
            meternal_medication: maternalMedication,
            gbs: {
                ...gbsScreening,
                status: 'save',
                last_modified_username: username,
                last_modified_date: now
            },
            other: formData.maternal_medication_other || []
        }
    };

    return payload;
};

export default mapSection2Data;
