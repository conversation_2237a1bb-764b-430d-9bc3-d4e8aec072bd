export const mapSection2Data = (formData, name, TNR, hospital) => {
    const now = new Date().toISOString();
    const username = name || 'system';

    // Helper function to map array of selected options to object with only "yes" values
    const mapOptionsToYesOnly = (selectedOptions, optionMapping = {}) => {
        const result = {};

        selectedOptions?.forEach(option => {
            // Use mapping if provided, otherwise use option as-is
            const mappedKey = optionMapping[option] || option;
            result[mappedKey] = 'yes';
        });

        return result;
    };

    // Map GBS screening - only include selected options
    const gbsScreeningMapping = {
        'na': 'NA'
    };
    const gbsScreening = mapOptionsToYesOnly(formData.gbs_screening || [], gbsScreeningMapping);

    // If 'other' is selected in GBS, add the other value
    if (formData.gbs_screening?.includes('other') && formData.gbs_screening_other) {
        gbsScreening.other = formData.gbs_screening_other;
    }

    // Map abnormal serology - only include selected options
    const abnormalSerologyMapping = {
        'positive_anti_hiv': 'positive_anti_HIV',
        'positive_hbsag': 'positive_HBsAg',
        'reactive_vdrl': 'positive_VDRL',
        'na': 'NA'
    };
    const abnormalSerology = mapOptionsToYesOnly(formData.abnormal_serology || [], abnormalSerologyMapping);

    if (formData.abnormal_serology?.includes('other') && formData.abnormal_serology_other) {
        abnormalSerology.other = formData.abnormal_serology_other;
    }

    // Map complications during pregnancy - only include selected options
    const pregnancyComplicationsMapping = {
        'no_anc': 'no_ANC',
        'overt_dm_gdm': 'over_DM_or_GDM',
        'chronic_ht': 'chronic_HT_or_pregnency_induced_HT_or_pre_eclampsia_or_eclampsia',
        'maternal_thyroid': 'meternal_thyroid_disease',
        'maternal_uti': 'meternal_UTI',
        'maternal_drug_abuse': 'meternal_drug_abuse',
        'na': 'NA'
    };
    const pregnancyComplications = mapOptionsToYesOnly(formData.complications_pregnancy || [], pregnancyComplicationsMapping);

    // Add additional fields for pregnancy complications
    if (formData.complications_pregnancy?.includes('multiple_gestation')) {
        pregnancyComplications.multiple_gestation = 'yes';
        // Add number field for twin or higher order
        if (formData.multiple_gestation_type === 'twin') {
            pregnancyComplications.number = 'Twin';
        } else if (formData.multiple_gestation_type === 'higher_order' && formData.multiple_gestation_higher_order_specify) {
            pregnancyComplications.number = formData.multiple_gestation_higher_order_specify;
        }
    }

    if (formData.complications_pregnancy?.includes('maternal_drug_abuse') && formData.maternal_drug_abuse_specify) {
        pregnancyComplications.meternal_drug_abuse = formData.maternal_drug_abuse_specify;
    }

    if (formData.complications_pregnancy?.includes('other') && formData.complications_pregnancy_other) {
        pregnancyComplications.other = formData.complications_pregnancy_other;
    }

    // Add metadata if any complications are selected
    if (Object.keys(pregnancyComplications).length > 0) {
        pregnancyComplications.status = 'save';
        pregnancyComplications.last_modified_username = username;
        pregnancyComplications.last_modified_date = now;
    }

    // Map intrapartum complications - only include selected options
    const intrapartumComplicationsMapping = {
        'fetal_distress': 'Fetal_distress_or_abnormal_NST_or_Decreased_fetal_movement',
        'maternal_fever': 'meternal_fever_or_Chrioaminonitis',
        'prolonged_rupture': 'prolonged_rupture_of_membrane_more_than_18_hr',
        'msaf': 'MSAF',
        'iugr': 'IUGR',
        'oligohydramios': 'oilgohydramios',
        'polyhydramios': 'polyhydramios',
        'na': 'NA'
    };
    const intrapartumComplications = mapOptionsToYesOnly(formData.intrapartum_complications || [], intrapartumComplicationsMapping);

    // Add other intrapartum complications if any
    if (formData.intrapartum_complications?.includes('other') && formData.intrapartum_complications_other) {
        intrapartumComplications.other = formData.intrapartum_complications_other;
    }

    // Add metadata if any complications are selected
    if (Object.keys(intrapartumComplications).length > 0) {
        intrapartumComplications.status = 'save';
        intrapartumComplications.last_modified_username = username;
        intrapartumComplications.last_modified_date = now;
    }

    // Map maternal medication - only include selected options
    const maternalMedicationMapping = {
        'dexamethasone_prenatal_steroid': 'dexamamthasone_or_prenatal_steroid',
        'mgso4': 'MgSo4',
        'no_medication': 'no_complication',
        'na': 'NA'
    };
    const maternalMedication = mapOptionsToYesOnly(formData.maternal_medication || [], maternalMedicationMapping);

    // Add timing for antibiotics if selected
    if (formData.maternal_medication?.includes('antibiotics')) {
        maternalMedication.antibiotics = 'yes';
        if (formData.antibiotics_timing) {
            maternalMedication.time = formData.antibiotics_timing;
        }
    }

    // Add course for prenatal steroid if selected
    if (formData.maternal_medication?.includes('dexamethasone_prenatal_steroid') && formData.prenatal_steroid_course) {
        maternalMedication.course = formData.prenatal_steroid_course;
    }

    // Add other medications if any (filter out empty values for payload)
    if (formData.maternal_medication?.includes('other') && formData.maternal_medication_other?.length > 0) {
        const nonEmptyOthers = formData.maternal_medication_other.filter(item => item && item.trim() !== '');
        if (nonEmptyOthers.length > 0) {
            maternalMedication.other = nonEmptyOthers.join(', ');
        }
    }

    // Add metadata if any medications are selected
    if (Object.keys(maternalMedication).length > 0) {
        maternalMedication.status = 'save';
        maternalMedication.last_modified_username = username;
        maternalMedication.last_modified_date = now;
    }

    // Add metadata to GBS screening if any options are selected
    if (Object.keys(gbsScreening).length > 0) {
        gbsScreening.status = 'save';
        gbsScreening.last_modified_username = username;
        gbsScreening.last_modified_date = now;
    }

    // Add metadata to abnormal serology if any options are selected
    if (Object.keys(abnormalSerology).length > 0) {
        abnormalSerology.status = 'save';
        abnormalSerology.last_modified_username = username;
        abnormalSerology.last_modified_date = now;
    }

    // Build the main data object - only include fields that have values
    const data = {
        TNR: TNR,
        status: 'save',
        last_modified_username: username,
        last_modified_date: now
    };

    // Add maternal age and age_NA fields
    if (formData.maternal_age_type === 'na') {
        data.age_NA = 'yes';
    } else if (formData.maternal_age) {
        data.age = formData.maternal_age;
    }

    // Add GPA values and NA fields
    if (formData.gpa_type === 'na') {
        data.G_NA = 'yes';
        data.P_NA = 'yes';
        data.A_NA = 'yes';
    } else {
        if (formData.gpa_g) {
            data.G = formData.gpa_g;
        } else {
            data.G_NA = 'yes';
        }
        if (formData.gpa_p) {
            data.P = formData.gpa_p;
        } else {
            data.P_NA = 'yes';
        }
        if (formData.gpa_a) {
            data.A = formData.gpa_a;
        } else {
            data.A_NA = 'yes';
        }
    }

    // Construct the final payload - only include sections that have data
    const payload = { data };

    if (Object.keys(abnormalSerology).length > 0) {
        payload.data.abnormal_serology = abnormalSerology;
    }

    if (Object.keys(pregnancyComplications).length > 0) {
        payload.data.complication_during_pregnancy = pregnancyComplications;
    }

    if (Object.keys(intrapartumComplications).length > 0) {
        payload.data.intrapartum_complication = intrapartumComplications;
    }

    if (Object.keys(maternalMedication).length > 0) {
        payload.data.meternal_medication = maternalMedication;
    }

    if (Object.keys(gbsScreening).length > 0) {
        payload.data.gbs = gbsScreening;
    }

    return payload;
};

export default mapSection2Data;
