
export const getStatusProps = (status, refStatus) => {
  if (status === "active") {
    switch (refStatus) {
      case "accept":
        return { text: "Accept", color: "#75ACE6" };
      case "non-mem refer":
        return { text: "Non-Mem Refer", color: "#5CA9F3" };
      case "reject":
        return { text: "Rejected", color: "#E5706A" };
      case "cancel":
        return { text: "Cancelled", color: "#B0B0B0" };
      case "waiting":
        return { text: "Waiting for refer", color: "#E5CC62" };
      default:
        return { text: "Recording", color: "#007AFF" };
    }
  }

  // Inactive
  switch (refStatus) {
    case "inactive":
      return { text: "discharge", color: "#B0B0B0" };
    case "inactive_refer":
      return { text: "referred", color: "#B0B0B0" };
    default:
      return { text: "Inactive", color: "#B0B0B0"  };
  }
};