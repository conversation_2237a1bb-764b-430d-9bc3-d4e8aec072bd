// English month names
const ENGLISH_MONTHS = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

// Month options in English
export const getMonthOptions = () => {
  return ENGLISH_MONTHS.map((month, i) => ({
    label: month,
    value: String(i + 1).padStart(2, "0"), // 01, 02 ... 12
  }));
};

// Year options with Thai year labels but Gregorian values
export const getYearOptionsRange = (pastYears = 4, futureYears = 10) => {
  const currentYear = new Date().getFullYear(); // Gregorian year
  const start = currentYear - pastYears;
  const end = currentYear + futureYears;

  return Array.from({ length: end - start + 1 }, (_, i) => {
    const gregorianYear = start + i;
    const thaiYear = gregorianYear + 543; // Convert to BE
    return { label: `${thaiYear}`, value: `${gregorianYear}` };
  });
};

// Day options depending on month and year
export const getDayOptions = (year, month) => {
  const m = month ? parseInt(month, 10) : null;
  const y = year ? parseInt(year, 10) : null;

  let max;
  if (!m) {
    max = 31; // default if month not provided
  } else if (!y) {
    // No year → assume non-leap for Feb
    max = m === 2 ? 28 : [4, 6, 9, 11].includes(m) ? 30 : 31;
  } else {
    // Leap year safe: day 0 of next month = last day of target month
    max = new Date(y, m, 0).getDate();
  }

  return Array.from({ length: max }, (_, i) => {
    const day = i + 1;
    return { label: `${day}`, value: String(day).padStart(2, "0") };
  });
};
