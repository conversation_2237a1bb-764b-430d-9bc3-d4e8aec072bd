// src/screens/HomeScreen.jsx
import React, { useContext, useEffect } from 'react';
import { View, StyleSheet, SafeAreaView, Text } from 'react-native';
import ScreenLoader from '../../components/ScreenLoader';
import { useDispatch, useSelector } from 'react-redux';
import ProfileHeader from './ProfileHeader';
import MenuList from './MenuList';
import { fetchUserData } from '../../features/home/<USER>';
import BgHome from '../../assets/img/bghome.svg';
import colors from '../../theme/colors';
import LanguageContext from '../../context/LanguageContext';

const HEADER_HEIGHT = 300;
const ROLE_BUTTON_HEIGHT = 28;

const HomeScreen = () => {
    const dispatch = useDispatch();
    const { name, hospital, role, loading, error } = useSelector(state => state.home);
    const { t } = useContext(LanguageContext);


    useEffect(() => {
        dispatch(fetchUserData());
    }, [dispatch]);


    return (
        // <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
            <View style={styles.headerWrapper}>
                <BgHome style={StyleSheet.absoluteFill} width="100%" height="100%" preserveAspectRatio="none" />
                <View style={styles.loadingContent}>
                    {loading ? (
                        <ScreenLoader visible={loading} />
                    ) : (
                        <ProfileHeader name={name} hospital={hospital} />
                    )}
                </View>
            </View>

            {!loading && (
                <View style={styles.roleButtonContainer}>
                    <View style={styles.roleButton}>
                        <Text style={styles.roleButtonText}>{role}</Text>
                    </View>
                </View>
            )}
            
            <MenuList t={t} />
        </View>
        // {/* </SafeAreaView> */ }
    );
};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: colors.background.white, // or your preferred background
    },
    container: {
        flex: 1,
    },
    headerWrapper: {
        height: HEADER_HEIGHT,
        backgroundColor: colors.background.white,

    },
    roleButtonContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: HEADER_HEIGHT - ROLE_BUTTON_HEIGHT / 2,
        alignItems: 'center',
        zIndex: 10,
    },
    roleButton: {
        backgroundColor: colors.button.role,
        borderRadius: 16,
        paddingHorizontal: 32,
        height: ROLE_BUTTON_HEIGHT,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: colors.shadow.white,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
    },
    roleButtonText: {
        color: colors.button.text,
        fontWeight: 'bold',
        fontSize: 14,
    },
    loadingContent: {
        flex: 1,
        width: '100%',
    },
    errorText: {
        color: colors.text.error,
        textAlign: 'center',
        marginTop: 32,
    },
});

export default HomeScreen;
