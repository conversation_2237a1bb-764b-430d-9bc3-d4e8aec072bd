import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import colors from '../../theme/colors';
import fonts from '../../theme/fonts';
import Next3Svg from '../../assets/img/next3.svg';
import listSvg from '../../assets/img/list.svg'
import ExportSvg from '../../assets/img/export.svg';
import HelpSvg from '../../assets/img/help.svg';
import SettingSvg from '../../assets/img/setting.svg';
import LogoutSvg from '../../assets/img/logout.svg';
import Approve2Svg from '../../assets/img/approve2.svg'
import SearchSvg from '../../assets/img/search2.svg'
import AsyncStorage from '@react-native-async-storage/async-storage';
import PopupCard from '../../components/PopupCard';
import WaitApproveSvg from '../../assets/img/waitapprove.svg';
import { useDispatch } from 'react-redux';
import { logout } from '../../features/auth/authSlice';
import ROUTES from '../../config/routes';

const menuItems = [
    { label: 'Patients List', icon: '≡', svg: listSvg, route: ROUTES.PRIVATE.PATIENT_LIST },
    { label: 'Search TN Number', svg: SearchSvg, route: ROUTES.PRIVATE.SEARCH_TN_NUMBER },
    { label: 'Export', icon: '⬇️', svg: ExportSvg },
    { label: 'Help & Support ', icon: '❓', svg: HelpSvg },
    { label: 'Approve User Account', svg: Approve2Svg },
    { label: 'Settings', icon: '⚙️', svg: SettingSvg,route: ROUTES.PRIVATE.SETTINGS },
    { label: 'Logout', icon: '↩️', svg: LogoutSvg },
];

const MenuList = ({ t }) => {
    const router = useRouter();
    const [logoutVisible, setLogoutVisible] = useState(false);
    const dispatch=useDispatch()

    const handleMenuPress = async (route, isLogout) => {
        if (isLogout) {
            setLogoutVisible(true)
            return;
        }
        if (route) {
            router.push(route);
        }
    };

    const handleLogoutConfirm = async () => {
        dispatch(logout())
        setLogoutVisible(false)
    };

    const handleLogoutCancel = () => {
        setLogoutVisible(false);
    };

    return (
        <View style={styles.menuContainer}>
            {menuItems.map((item, idx) => {
                const IconSvg = item.svg;
                return (
                    <TouchableOpacity
                        key={item.label}
                        style={[
                            styles.menuItem,
                            item.danger && styles.dangerItem,
                        ]}
                        onPress={() => handleMenuPress(item.route, item.label === 'Logout')}
                        activeOpacity={0.7}
                    >
                        <View style={styles.contentContainer}>
                            <View style={styles.iconContainer}>
                                {IconSvg ? (
                                    <IconSvg width={24} height={24} />
                                ) : (
                                    <Text style={[styles.icon, item.danger && styles.dangerText]}>{item.icon}</Text>
                                )}
                            </View>
                            <View style={styles.rightContainer}>
                                <Text style={[styles.menuText, item.danger && styles.dangerText, fonts.menu]}>{t.t(item.label)}</Text>
                                <View style={styles.arrowContainer}>
                                    <Next3Svg width={16} height={16} />
                                </View>
                            </View>
                        </View>
                    </TouchableOpacity>
                );
            })}
            {logoutVisible && (
                <PopupCard
                    title={t.t('Log out')}
                    description={t.t('Do you want to log out?')}
                    backgroundColor="#c7b09a"
                    svg={<WaitApproveSvg width={168} height={122} />}
                    buttons={[
                        { label: t.t('No'), onPress: handleLogoutCancel, color: colors.text.cardText },
                        { label: t.t('Yes'), onPress: handleLogoutConfirm, color: colors.text.cardText }
                    ]}
                />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    menuContainer: {
        flex: 1,
        backgroundColor: colors.background.white,
        paddingHorizontal: 0,
        paddingTop: 30,
    },
    menuItem: {
        alignItems: "center"
    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingLeft: 12,
        paddingRight: 12
    },
    iconContainer: {
        width: 32,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 8,
    },
    lastItem: {
        borderBottomWidth: 0,
    },
    arrowContainer: {
        width: 24,
        alignItems: 'center',
        justifyContent: 'center',
    },
    arrow: {
        fontSize: 20,
        color: colors.placeholder,
    },
    icon: {
        fontSize: 20,
    },
    menuText: {
        flex: 1,
        fontSize: fonts.menu.fontSize,
    },
    rightContainer: {
        flex: 1,
        flexDirection: 'row',
        borderColor: colors.border.main,
        borderBottomWidth: 1,
        paddingBottom: 12,
        paddingTop: 12,
    },
});

export default MenuList; 