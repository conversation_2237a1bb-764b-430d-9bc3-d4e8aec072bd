import React, { useContext } from 'react';
import { View, Text, StyleSheet } from 'react-native';
// import StarGreenSvg from '../../assets/img/stargreen.svg';
// import StarGraySvg from '../../assets/img/stargray.svg';
import ActiveGray from '../../assets/img/activegray.svg';
import ActiveGreen from '../../assets/img/activegreen.svg';
import InactiveGray from '../../assets/img/inactivegray.svg';
import InactiveGreen from '../../assets/img/inactivegreen.svg';
import colors from '../../theme/colors';
import fonts from '../../theme/fonts';
import { responsive } from '../../utils/responsive';
import LanguageContext from '../../context/LanguageContext';

const Footer = ({ active, inactive, onActivePress, onInactivePress }) => {
    const { t } = useContext(LanguageContext);
    
    return (
        <View style={styles.footerContainer}>
            <View style={styles.tab}>
                {active ? (
                    <ActiveGreen width={24} height={24} onPress={onActivePress} />
                ) : (
                    <ActiveGray width={24} height={24} onPress={onActivePress} />
                )}
                <Text style={[styles.tabText, active && styles.activeText]}>{t.t('Active')}</Text>
            </View>
            <View style={styles.tab}>
                {inactive ? (
                    <InactiveGreen width={24} height={24} onPress={onInactivePress} />
                ) : (
                    <InactiveGray width={24} height={24} onPress={onInactivePress} />
                )}
                <Text style={[styles.tabText, inactive && styles.activeText]}>{t.t('Inactive')}</Text>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    footerContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        alignItems: 'center',
        paddingVertical: responsive(12),
        borderTopWidth: 1,
        borderColor: '#eee',
        backgroundColor: colors.footer.background,
    },
    tab: {
        alignItems: 'center',
    },
    tabText: {
        fontSize: fonts.footer.textSize,
        color: colors.text.footerTextGray,
        marginTop: 4,
    },
    activeText: {
        color: colors.text.footerTextGreen,
        fontWeight: 'bold',
    },
});

export default Footer; 
