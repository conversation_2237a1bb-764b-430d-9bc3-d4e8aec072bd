import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput } from 'react-native';
import { responsive } from '../../utils/responsive';
import BackSvg from '../../assets/img/back.svg';
import Icon1Svg from '../../assets/img/icon1.svg';
import Icon2Svg from '../../assets/img/icon2.svg';
import SearchSvg from '../../assets/img/search.svg';
import colors from '../../theme/colors';
import LanguageContext from '../../context/LanguageContext';

const Header = ({ onBack, onSort, onFilter, onSearch, searchValue, iconRef }) => {
    const { t } = useContext(LanguageContext);

    return (
        <View style={styles.headerContainer}>
            {/* Nav Bar */}
            <View style={styles.navBar}>
                <TouchableOpacity onPress={onBack} style={styles.backButtonRow}>
                    <BackSvg width={11} height={20} />
                    <Text style={styles.backText}>{t.t('Back')}</Text>
                </TouchableOpacity>
                <View style={{ flexDirection: 'row', marginLeft: 'auto' }}>
                    <TouchableOpacity onPress={onFilter} style={styles.iconButton}>
                        <Icon1Svg width={28} height={28} />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={onSort} style={styles.iconButton} ref={iconRef}>
                        <Icon2Svg width={28} height={28} />
                    </TouchableOpacity>
                </View>
            </View>
            {/* Heading and Search Bar */}
            <View style={styles.headingSearchContainer}>
                <Text style={styles.heading}>{t.t('Patients List')}</Text>
                <View style={styles.searchContainer}>
                    <SearchSvg width={18} height={18} style={styles.searchIcon} />
                    <TextInput
                        style={styles.searchBar}
                        placeholder={t.t('Search patients')}
                        value={searchValue}
                        onChangeText={onSearch}
                        placeholderTextColor="#8E8E93"
                    />
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    headerContainer: {
        paddingHorizontal: responsive(12),
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#eee'
    },
    navBar: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    iconButton: {
        padding: responsive(8),
        marginHorizontal: 4,
    },
    backText: {
        fontSize: 18,
        color: colors.text.navBarGreen,
        marginLeft: 4,
    },
    headingSearchContainer: {
        flexDirection: 'column',
        marginBottom: 13,
    },
    heading: {
        fontSize: 28,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    searchContainer: {
        position: 'relative',
        justifyContent: 'center',
    },
    searchBar: {
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
        paddingVertical: responsive(10),
        paddingRight: responsive(10),
        paddingLeft: responsive(36),
        fontSize: 16,
    },
    searchIcon: {
        position: 'absolute',
        left: responsive(12),
        zIndex:100
    },
    backButtonRow: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: responsive(8),
        paddingRight: responsive(8),
        paddingLeft: responsive(0),
        marginHorizontal: 0,
    },
});

export default Header; 
