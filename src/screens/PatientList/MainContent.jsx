import React, { useEffect, useCallback, useState, useRef, useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Next2Svg from '../../assets/img/next2.svg';
import { responsive } from '../../utils/responsive';
import { useSelector, useDispatch } from 'react-redux';
import { fetchPatients, resetPatients, resetSortedPatients } from '../../features/patients/patientsSlice';
import { setTNR, setPatientData } from '../../features/register/registerSlice';
import { useRouter } from 'expo-router';
import ScreenLoader from '../../components/ScreenLoader';
import colors from '../../theme/colors';
import LanguageContext from '../../context/LanguageContext';
import ROUTES from '../../config/routes';
import ListItem from '../../components/ListItem';

const MainContent = ({ referList, patientDataToShow, activeTab, onPatientPress, sortField,
    sortOrder, onLoadMoreSorted, sortSelected }) => {
    const dispatch = useDispatch();
    const { data: patientList, loading, offset, hasMore, loadingMore, loadingMoreSort, offsetSort, hasMoreSort } = useSelector(state => state.patients);
    const router = useRouter();
    const { t } = useContext(LanguageContext);
    const listToShow = patientDataToShow && patientDataToShow.length > 0 ? patientDataToShow : patientList;

    // Fetch patients on mount and when activeTab changes
    useEffect(() => {
        dispatch(resetPatients());
        // dispatch(fetchPatients({ status: activeTab, offset: 0 }));
    }, [activeTab]);

    const handleEndReached = useCallback(() => {
        if (loading || loadingMore || (sortSelected && loadingMoreSort)) return;

        if (sortSelected) {
            if (hasMoreSort && offsetSort > 0) {
                onLoadMoreSorted(offsetSort);
            }
            return;
        }
        if (hasMore && offset > 0) {
            dispatch(fetchPatients({ status: activeTab, offset }));
        }
    }, [
        loading,
        loadingMore,
        sortField,
        sortOrder,
        hasMoreSort,
        offsetSort,
        loadingMoreSort,
        hasMore,
        offset,
        activeTab,
        dispatch,
        onLoadMoreSorted,
    ]);


    const handleRoute = () => {
        router.replace('/privateRoutes/register-criteria')
    }

    // const handlePatientPress = useCallback((patient) => {
    //     dispatch(setTNR(patient.TNR));
    //     // isReturningFromPatientDisplay.current = true; // ✅ Set return flag
    //     router.push(ROUTES.PRIVATE.PATIENT_DISPLAY_SCREEN);
    // }, [dispatch, router]);

    const renderPatientItem = useCallback(({ item }) => {
        return (
            <ListItem
                title={item.fullname}
                subtitle={`TN#: ${item.TNR}`}
                status={activeTab}
                refer_status={item.refer_status}
                onPress={() => onPatientPress(item)}
            />
        );
    }, [onPatientPress]);

    const keyExtractor = useCallback((item, idx) => item.id?.toString() || idx.toString(), []);

    return (
        <View style={styles.mainContentContainer}>
            {/* Refer List Section */}
            {referList && referList.length > 0 && (
                <TouchableOpacity onPress={() => router.push(ROUTES.PRIVATE.REFER_LIST)}>
                    <View style={styles.referSection}>
                        <View style={styles.referRow}>
                            <Text style={styles.referTitle}>{t.t('Patients referred to your hospital')}</Text>
                            <View style={styles.referBadgeArrowContainer}>
                                <Text style={styles.referBadge}>{referList.length}</Text>
                                <Next2Svg width={14} height={14} style={styles.chevronIcon} />
                            </View>
                        </View>
                    </View>
                </TouchableOpacity>
            )}
            {/* Patient List Section */}

            {// Show FlatList when loading is false
                listToShow && listToShow.length > 0 ? (
                    <FlatList
                        data={listToShow}
                        keyExtractor={keyExtractor}
                        renderItem={renderPatientItem}
                        onEndReached={handleEndReached}
                        InitialNumToRender={10}
                        maxToRenderPerBatch={10}
                        windowSize={5}
                        removeClippedSubviews={true}
                        onEndReachedThreshold={0.1}
                        ListFooterComponent={loadingMore ? <ActivityIndicator size="small" color="#6C5CE7" style={{ marginVertical: 20 }} /> : null}
                    />
                ) :
                    !loading ? (
                        <Text style={{ textAlign: 'center', marginTop: 20 }}>
                            {`${t.t('No patients found')}.`}
                        </Text>
                    ) : (
                        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                            <ScreenLoader visible />
                        </View>
                    )
            }
            {/* Count Section */}
            <Text style={styles.countText}>
                {activeTab === 'active'
                    // ? `${t.t('Number of Active patients')} ${listToShow?.length} ${t.t('people')}`
                    // : `${t.t('Number of Inactive patients')} ${listToShow?.length} ${t.t('people')}`}
                    ? `${t.t('Total active patients')} ${listToShow?.length}`
                    : `${t.t('Total inactive patients')} ${listToShow?.length}`}
            </Text>

            <TouchableOpacity style={styles.fab} onPress={handleRoute}>
                <Ionicons name="add" size={36} color="white" />
                <Text style={styles.text}>Add New Patient</Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    mainContentContainer: {
        flex: 4,
        backgroundColor: '#fff',
        position: "relative"
    },
    referSection: {
        backgroundColor: '#f5f5f5',
        padding: 18,
    },
    referRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingRight: 4,
    },
    referBadgeArrowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    referTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    referBadge: {
        width: 24,
        height: 24,
        backgroundColor: 'red',
        color: 'white',
        borderRadius: 15,
        paddingHorizontal: responsive(8),
        marginLeft: 4,
        fontSize: 15,
    },
    countText: {
        marginTop: 15,
        textAlign: 'center',
    },
    fab: {
        position: 'absolute',
        right: 24,
        bottom: 24,
        backgroundColor: colors.button.primary,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 4,
        flexDirection: "row",
        paddingHorizontal: 10,
        paddingVertical: 4
    },
    text: {
        color: "white"
    },
    patientRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingLeft: responsive(18),
        paddingRight: responsive(18),
        paddingTop: responsive(10),
        paddingBottom: responsive(10),
    },
    patientInfo: {
        flex: 1,
    },
    patientName: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    patientTn: {
        fontSize: 14,
        color: '#888',
    },
    statusRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusBadge: {
        backgroundColor: '#007AFF',
        borderRadius: 8,
        paddingHorizontal: responsive(8),
        paddingVertical: responsive(4),
        marginRight: 8,
    },
    statusBadgeText: {
        fontSize: 12,
        color: '#fff',
    },
    chevronIcon: {
        marginLeft: 4,
    },
});

export default MainContent; 
