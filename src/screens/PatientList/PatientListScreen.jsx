import React, { useContext, useEffect, useRef, useState, useCallback } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from '@react-navigation/native';

import Header from './Header';
import MainContent from './MainContent';
import Footer from './Footer';
import colors from '../../theme/colors';
import ROUTES from '../../config/routes';
import SortPopup from '../../components/SortPopup';
import LanguageContext from '../../context/LanguageContext';
import { debounce } from 'lodash';
import { resetCriteria, setTNR } from '../../features/register/registerSlice';
import { searchPatients, resetPatients, fetchPatients, setSearchValue, setCameFromPatientDisplay, fetchReferredPatients, fetchSortedPatientsThunk, resetSortedPatients, setOffsetSortToZero } from '../../features/patients/patientsSlice';

const mockReferList = [
    { id: 1, name: 'เด็กชายสมมุติ ร่างกายอ่อนแอ', tn: 'XXXXXXXXXX' },
    { id: 2, name: 'เด็กชายสมมุติ ร่างกายอ่อนแอ', tn: 'XXXXXXXXXX' },
    { id: 3, name: 'เด็กชายสมมุติ ร่างกายอ่อนแอ', tn: 'XXXXXXXXXX' },
];

const PatientListScreen = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    // const [searchValue, setSearchValue] = useState('');
    const [activeTab, setActiveTab] = useState('active');
    const { data, loading, cameFromPatientDisplay, searchValue: reduxSearchValue, referredPatients,offset,offsetSort } = useSelector(state => state.patients);
    // To preserve search value only within same session
    const searchValueRef = useRef('');
    const [sortModalVisible, setSortModalVisible] = useState(false);
    const [selectedSort, setSelectedSort] = useState({
        sortField: 'admit_date',
        sortOrder: 'DESC',
    });
    const [sortSelected,setSortSelected]=useState(false)
    const { t } = useContext(LanguageContext)
    const iconRef = useRef();
    const sortSections = [
    {
        key: 'sortField',
        title: t.t('Sort Field'),
        options: [
            { label: t.t('Patient Name'), value: 'patient_name' },
            { label: t.t('TN Number'), value: 'tn_number' },
            { label: t.t('Admit Date'), value: 'admit_date' },
        ],
    },
    {
        key: 'sortOrder',
        title: t.t('Sort Order'),
        options: [
            { label: t.t('Ascending'), value: 'ASC' },
            { label: t.t('Descending'), value: 'DESC' },
        ],
    },
];


    const handleSortOpen = () => {
        // dispatch(setOffsetSortToZero())
        setSortModalVisible(true);
    };

    const handleSortApply = () => {
        setSortSelected(true)
        dispatch(resetSortedPatients())
        dispatch(
            fetchSortedPatientsThunk({
                status: activeTab,
                sortField: selectedSort.sortField || 'tn_number',
                sortOrder: selectedSort.sortOrder || 'DESC',
                offset: 0,
                limit: 10,
            }))
        setSortModalVisible(false);
    };

    const handleSortSelect = (sectionKey, value) => {
        setSelectedSort((prev) => ({ ...prev, [sectionKey]: value }));
    };

    useEffect(() => {
        dispatch(resetCriteria())
        dispatch(fetchReferredPatients()); 
    }, [])

    useFocusEffect(
        useCallback(() => {
            if (cameFromPatientDisplay) {
                dispatch(setCameFromPatientDisplay(false)); // Reset flag after processing

                if (reduxSearchValue) {
                    dispatch(searchPatients(reduxSearchValue));
                    return;
                }
            }
            // No previous filter/search — load full list
            dispatch(setSearchValue(''));
            searchValueRef.current = '';
            dispatch(resetPatients());
            dispatch(fetchPatients({ status: activeTab.toLowerCase(), offset: 0 }));
        }, [activeTab])
    );


    const debouncedSearch = useRef(
        debounce((text) => {
            dispatch(searchPatients({
                status: activeTab.toLowerCase(),
                search: text,
                limit: 10,
                offset: 0
            }));
        }, 300)
    ).current;

    const handleBack = () => {
        router.replace(ROUTES.PRIVATE.HOME);
    };
    const handleSort = () => { };
    const handleFilter = () => { };
    const handleSearch = (text) => {
        dispatch(setSearchValue(text));
        searchValueRef.current = text;
        dispatch(searchPatients(text));
    }
    const handleActivePress = () => {
        dispatch(resetSortedPatients())
        setSortSelected(false)
        setActiveTab('active');
    }
    const handleInactivePress = () => {
        setActiveTab('inactive');
        setSortSelected(false)
        dispatch(resetSortedPatients())
        dispatch(searchPatients({
            status: 'inactive',
            search: searchValueRef.current,
            limit: 10,
            offset: 0
        }));
    }
    const handlePatientPress = (patient) => {
        dispatch(setCameFromPatientDisplay(true));
        dispatch(setTNR(patient.TNR));
        router.push(ROUTES.PRIVATE.PATIENT_DISPLAY_SCREEN);
    };

    // Filtered lists based on search and tab
    // const filteredReferList = mockReferList.filter(item => item.name.includes(reduxSearchValue));
   const filteredReferList = (reduxSearchValue
    ? referredPatients.filter(item =>
        (item.fullname || '').toLowerCase().includes(reduxSearchValue.toLowerCase()) ||
        (item.TNR || '').toLowerCase().includes(reduxSearchValue.toLowerCase())
        )
    : referredPatients
    ).map((item, index) => ({
    ...item,
    name: item.fullname, // for compatibility
    id: index + 1 // ensure keyExtractor works
    }));



    const filteredPatientList = reduxSearchValue
        ? data.filter(item => {
            const nameMatch = (item.fullname || '').toLowerCase().includes(reduxSearchValue.toLowerCase());
            const tnrMatch = (item.TNR || '').toLowerCase().includes(reduxSearchValue.toLowerCase());
            return nameMatch || tnrMatch;
        })
        : data;

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.background.white }} edges={["top"]}>
            <View style={styles.container}>
                {/* <Header
                    onBack={handleBack}
                    onSort={handleSort}
                    onFilter={handleFilter}
                    onSearch={handleSearch}
                    searchValue={searchValue}
                /> */}
                <Header
                    onBack={handleBack}
                    onSort={handleSortOpen}
                    onFilter={handleFilter}
                    onSearch={handleSearch}
                    searchValue={reduxSearchValue}
                    iconRef={iconRef}
                />

                <View style={styles.mainContentWrapper}>
                    <MainContent
                        referList={filteredReferList}
                        sortSelected={sortSelected}
                        patientDataToShow={filteredPatientList}
                        activeTab={activeTab}
                        sortField={selectedSort.sortField}
                        sortOrder={selectedSort.sortOrder}
                        onPatientPress={handlePatientPress}
                        onLoadMoreSorted={() => {
                            dispatch(
                                fetchSortedPatientsThunk({
                                    status: activeTab,
                                    sortField: selectedSort.sortField,
                                    sortOrder: selectedSort.sortOrder,
                                    offset: offsetSort||0,
                                    limit: 10,
                                })
                            );
                        }}
                    />
                </View>
                <Footer
                    active={activeTab === 'active'}
                    inactive={activeTab === 'inactive'}
                    onActivePress={handleActivePress}
                    onInactivePress={handleInactivePress}
                />
                <SafeAreaView style={{ backgroundColor: colors.footer.background }} edges={["bottom"]} />

                <SortPopup
                    visible={sortModalVisible}
                    onClose={() => setSortModalVisible(false)}
                    onApply={handleSortApply}
                    onSelectOption={handleSortSelect}
                    selectedValues={selectedSort}
                    cancelText={t.t('Cancel')}
                    applyText={t.t('Apply')}
                    anchorRef={iconRef}
                    sections={sortSections}
                />
 
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    mainContentWrapper: {
        flex: 1,
    },
});

export default PatientListScreen; 