import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Next2Svg from '../../assets/img/next2.svg';
import { responsive } from '../../utils/responsive';
import LanguageContext from '../../context/LanguageContext';

const PatientItem = React.memo(({ item, onPress }) => {
    const { t } = useContext(LanguageContext);
    
    return (
        <TouchableOpacity style={styles.patientRow} onPress={() => onPress(item)}>
            <View style={styles.patientInfo}>
                <Text style={styles.patientName}>{item.fullname}</Text>
                <Text style={styles.patientTn}>TN#: {item.TNR}</Text>
            </View>
            <View style={styles.statusRow}>
                <View style={styles.statusBadge}>
                    <Text style={styles.statusBadgeText}>{t.t('Recording')}</Text>
                </View>
                <Next2Svg width={14} height={14} style={styles.chevronIcon} />
            </View>
        </TouchableOpacity>
    );
});


const styles = StyleSheet.create({
    mainContentContainer: {
        flex: 4,
        backgroundColor: '#fff',
        position: "relative"
    },
    referSection: {
        backgroundColor: '#f5f5f5',
        padding: 18,
    },
    referRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingRight: 4,
    },
    referBadgeArrowContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    referTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 8,
    },
    referBadge: {
        width: 24,
        height: 24,
        backgroundColor: 'red',
        color: 'white',
        borderRadius: 15,
        paddingHorizontal: responsive(8),
        marginLeft: 4,
        fontSize: 15,
    },
    countText: {
        marginTop: 15,
        textAlign: 'center',
    },
    fab: {
        position: 'absolute',
        right: 24,
        bottom: 24,
        backgroundColor: '#007AFF',
        borderRadius: 28,
        width: 56,
        height: 56,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 4,
    },
    patientRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingLeft: responsive(18),
        paddingRight: responsive(18),
        paddingTop: responsive(10),
        paddingBottom: responsive(10),
    },
    patientInfo: {
        flex: 1,
    },
    patientName: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    patientTn: {
        fontSize: 14,
        color: '#888',
    },
    statusRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusBadge: {
        backgroundColor: '#007AFF',
        borderRadius: 8,
        paddingHorizontal: responsive(8),
        paddingVertical: responsive(4),
        marginRight: 8,
    },
    statusBadgeText: {
        fontSize: 12,
        color: '#fff',
    },
    chevronIcon: {
        marginLeft: 4,
    },
});
export default PatientItem
