import React, { useState, useContext } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import BackSvg from '../../assets/img/back.svg';
import colors from '../../theme/colors';
import { responsive } from '../../utils/responsive';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import LanguageContext from '../../context/LanguageContext';

const ForgotPassword = () => {
    const [email, setEmail] = useState('');
    const router = useRouter();
    const { t } = useContext(LanguageContext);

    const handleBack = () => {
        router.replace('/publicRoutes/login');
    };

    const handleSubmit = () => {
        // Handle submit logic here
    };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: '#f7f7f7' }} edges={["top"]}>
            <View style={styles.container}>
                {/* Nav Bar */}
                <View style={styles.navBar}>
                    <TouchableOpacity style={styles.backRow} onPress={handleBack}>
                        <BackSvg width={11} height={20} />
                        <Text style={styles.backText}>{t.t('Back')}</Text>
                    </TouchableOpacity>
                    <Text style={styles.heading}>{t.t('Forgot password')}</Text>
                </View>
                {/* Main Content */}
                <View style={styles.mainContent}>
                    <Text style={styles.instructions}>
                        {t.t('Please enter the email you used to register to proceed with resetting your password')}
                    </Text>
                    <TextInput
                        style={styles.input}
                        placeholder={t.t('Email')}
                        placeholderTextColor={colors.text.placeholder}
                        value={email}
                        onChangeText={setEmail}
                    />
                    <TouchableOpacity style={styles.button} onPress={handleSubmit}>
                        <Text style={styles.buttonText}>{t.t('Confirm')}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f7f7f7',
    },
    navBar: {
        flexDirection: 'column',
        paddingTop: responsive(24),
        paddingHorizontal: responsive(18),
        paddingBottom: responsive(8),
        backgroundColor: '#f7f7f7',
    },
    backRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: responsive(8),
    },
    backText: {
        color: colors.text.navBar,
        fontSize: 18,
        marginLeft: 4,
    },
    heading: {
        fontSize: 28,
        fontWeight: 'bold',
        color: colors.text.main,
        marginBottom: responsive(8),
    },
    mainContent: {
        flex: 1,
        paddingHorizontal: responsive(18),
        alignItems: 'center',
    },
    instructions: {
        fontSize: 16,
        color: colors.text.main,
        textAlign: 'left',
        marginBottom: responsive(24),
        marginTop: responsive(8),
    },
    input: {
        width: '100%',
        backgroundColor: '#fff',
        borderRadius: 24,
        paddingHorizontal: 20,
        paddingVertical: 12,
        fontSize: 16,
        marginBottom: 18,
        borderWidth: 0,
        elevation: 1,
        shadowColor: '#000',
        shadowOpacity: 0.03,
        shadowRadius: 2,
        shadowOffset: { width: 0, height: 1 },
    },
    button: {
        width: '100%',
        backgroundColor: colors.button.primary,
        borderRadius: 24,
        paddingVertical: 13,
        alignItems: 'center',
        marginBottom: 18,
    },
    buttonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default ForgotPassword; 