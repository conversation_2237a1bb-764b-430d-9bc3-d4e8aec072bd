import React, { useContext } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import colors from '../../theme/colors'
import { responsive } from '../../utils/responsive';
import { useRouter } from 'expo-router';
import { useDispatch } from 'react-redux';
import { decrementStep, resetStep, unmarkStepCompleted } from '../../features/register/registerSlice';
import LanguageContext from '../../context/LanguageContext';

const Header = ({ currentStep }) => {
    const router = useRouter();
    const dispatch = useDispatch();
    const { t } = useContext(LanguageContext);

    const handleBack = () => {
        if (currentStep === 0) {
            dispatch(resetStep(0));
            router.replace('/privateRoutes/patientList');
            return;
        }
        // reset the fields of the current step
        dispatch(resetStep(currentStep));
        dispatch(unmarkStepCompleted(currentStep - 1));
        dispatch(decrementStep());
    };


    return (
        <View style={styles.header}>
            <View style={styles.backContainer}>
                <TouchableOpacity onPress={handleBack} style={styles.backButton}>
                    <Ionicons name="arrow-back" size={24} color="#6DC6C4" />
                    <Text style={styles.backText}>{t.t('Back')}</Text>
                </TouchableOpacity>
            </View>
            <Text style={styles.headerTitle}>{t.t('Registry Criteria')}</Text>
        </View>
    );
};

export default Header;

const styles = StyleSheet.create({
    header: {
        paddingHorizontal: responsive(20),
        paddingTop: responsive(20),
        paddingBottom: responsive(10),
        backgroundColor: colors.header.background,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.main
    },
    backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
    },
    backText: {
        fontSize: 16,
        color: colors.text.navBarGreen,
    },
    headerTitle: {
        marginTop: 6,
        fontSize: 34,
        fontWeight: '400',
        paddingLeft: 8,
    },
});
