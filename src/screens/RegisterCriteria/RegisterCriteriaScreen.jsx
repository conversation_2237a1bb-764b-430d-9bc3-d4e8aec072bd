import React, { useState, useContext } from 'react';
import {
    StyleSheet,
    View,
} from 'react-native';
import Header from './Header';
import Footer from './Footer';
import MainContent from './MainContent';
import ScreenWrapper from '../../components/ScreenWrapper';
import colors from '../../theme/colors';
import { useRouter } from 'expo-router';
import PopupCard from '../../components/PopupCard';
import FailLoginIcon from '../../assets/img/faillogin.svg';
import { useDispatch, useSelector } from 'react-redux';
import { incrementStep, markStepCompleted } from '../../features/register/registerSlice';
import LanguageContext from '../../context/LanguageContext';

const RegisterCriteriaScreen = () => {
    // const [currentStep, setCurrentStep] = useState(0);
    // const [completedSteps, setCompletedSteps] = useState([]);
    const registerCriteria = useSelector((state) => state.register);
    const [alertPopupVisible, setAlertPopupVisible] = useState(false);
    const router = useRouter()
    const dispatch = useDispatch();
    const {
        currentStep,
        completedSteps,
    } = useSelector(state => state.register);
    const { t } = useContext(LanguageContext);

    const handlePoupClose = () => {
        setAlertPopupVisible(false)
    }

    const handleNext = () => {
        if (currentStep === 0) {
            const pnaVal = Number(registerCriteria.pna);
            if (!Number.isFinite(pnaVal)) {
                setAlertPopupVisible(true);
                return;
            }
            if (pnaVal > 28) {
                router.push('/privateRoutes/validate-register-criteria');
                return;
            }
        }

        if (currentStep === 1) {
            if (!registerCriteria.symptoms || registerCriteria.symptoms.length === 0) {
                setAlertPopupVisible(true);
                return;
            }
            router.push('/privateRoutes/validate-register-criteria');
            return;
        }

        if (!completedSteps.includes(currentStep)) {
            dispatch(markStepCompleted(currentStep));
        }
        dispatch(incrementStep());
    };


    return (
        <ScreenWrapper topColor={colors.header.background}>
            <View style={styles.container}>
                <Header currentStep={currentStep} />
                <MainContent onNext={handleNext} />
                <Footer currentStep={currentStep} completedSteps={completedSteps} />
                <PopupCard
                    visible={alertPopupVisible}
                    title={'Incorrect'}
                    description={'Incomplete Information'}
                    backgroundColor={colors.card.redBackground}
                    svg={<FailLoginIcon width={168} height={122} />}
                    buttons={[
                        { label: 'Try Again', onPress: handlePoupClose, color: colors.text.red },
                    ]}
                />
            </View>
        </ScreenWrapper>
    );
};

export default RegisterCriteriaScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.background.white,
    },
});
