import { StyleSheet, Text, View } from "react-native"
import { Ionicons } from '@expo/vector-icons';
import { responsive } from "../../utils/responsive";
import colors from "../../theme/colors";

const Footer = ({ currentStep, completedSteps }) => {
    return (
        <View style={styles.footer}>
            {[1, 2, 3].map((step, idx) => {
                const isActive = idx === currentStep;
                const isCompleted = completedSteps.includes(idx);

                return (
                    <View key={idx} style={styles.stepContainer}>
                        <View style={[
                            styles.circle,
                            isActive && styles.circleActive,
                            isCompleted && styles.circleCompleted,
                        ]}>
                            {isCompleted ? (
                                <Ionicons name="checkmark" size={16} color="#fff" />
                            ) : (
                                <Text style={[
                                    styles.circleText,
                                    isActive && styles.circleTextActive
                                ]}>
                                    {step}
                                </Text>
                            )}
                        </View>
                        {idx < 2 && <View style={[
                            styles.line,
                            isCompleted && styles.lineCompleted
                        ]}
                        />}
                    </View>
                );
            })}
        </View>
    );
};


export default Footer

const styles = StyleSheet.create({
    footer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: responsive(15),
        backgroundColor: colors.background.white
    },
    stepContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    circle: {
        width: 28,
        height: 28,
        borderRadius: 14,
        borderWidth: 1,
        borderColor: '#ccc',
        justifyContent: 'center',
        alignItems: 'center',
    },
    circleActive: {
        backgroundColor: '#7FC5C6',
        borderColor: colors.border.green,
    },
    circleCompleted: {
        backgroundColor: '#7FC5C6',
        borderColor: colors.border.green,
    },
    circleText: {
        fontSize: 14,
        color: '#999',
    },
    circleTextActive: {
        color: '#fff',
        fontWeight: 'bold',
    },
    line: {
        width: 25,
        height: 1,
        backgroundColor: '#ccc',
    },
    lineCompleted: {
        backgroundColor: '#7FC5C6',
    },

});