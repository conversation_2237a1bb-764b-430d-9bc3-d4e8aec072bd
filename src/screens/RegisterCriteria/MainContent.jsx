import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from "react-native"
import globalStyles from "../../styles/globalStyles"
import colors from "../../theme/colors"
import { responsive } from "../../utils/responsive"
import { useState, useContext } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setPna, toggleSymptom } from "../../features/register/registerSlice";
import LanguageContext from "../../context/LanguageContext";

const MainContent = ({ onNext }) => {
    const dispatch = useDispatch();
    const registerCriteria = useSelector((state) => state.register);
    const { t } = useContext(LanguageContext);

    const steps = [
        {
            title: t.t('PNA (Postnatal Age)'),
            desc: t.t('Select the age from birth of the baby'),
            type: 'options',
            options: [
                { label: t.t('<= 28 days PNA'), value: 28 },
                { label: t.t('> 28 days PNA'), value: 29 },
            ],
        },
        {
            title: t.t('Patient symptoms'),
            desc: t.t('One of the following'),
            type: 'multi',
            options: [t.t('GA < 32 weeks GA'), t.t('BW < 1,500g'), t.t('HIE'), t.t('Sick born'), t.t('Normal newborn')],
        },
    ];
    const current = steps[registerCriteria.currentStep];
    if (!current) {
        return (
            <View style={styles.content}>
                <Text style={{ textAlign: 'center', marginTop: 20 }}>
                    {JSON.stringify(registerCriteria, null, 2)}
                </Text>
            </View>
        );
    }

    // Symptom grouping logic (mutually exclusive groups)
    const LABEL_GA = t.t('GA < 32 weeks GA');
    const LABEL_BW = t.t('BW < 1,500g');
    const LABEL_HIE = t.t('HIE');
    const LABEL_SICK = t.t('Sick born');
    const LABEL_NORMAL = t.t('Normal newborn');
    const GROUP_A = [LABEL_GA, LABEL_BW, LABEL_HIE];
    const selectedSymptoms = registerCriteria.symptoms || [];
    const isAnyGroupASelected = GROUP_A.some(s => selectedSymptoms.includes(s));
    const isSickSelected = selectedSymptoms.includes(LABEL_SICK);
    const isNormalSelected = selectedSymptoms.includes(LABEL_NORMAL);


    const handleMultiSelect = (option) => {
        const inGroupA = GROUP_A.includes(option);

        // If option is in Group A (GA/BW/HIE)
        if (inGroupA) {
            // Block selecting Group A if Sick or Normal is selected
            if (!selectedSymptoms.includes(option) && (isSickSelected || isNormalSelected)) return;
            dispatch(toggleSymptom(option));
            return;
        }

        // If option is Sick newborn
        if (option === LABEL_SICK) {
            // Allow deselect if already selected
            if (isSickSelected) {
                dispatch(toggleSymptom(option));
                return;
            }
            // Block selecting Sick if Group A or Normal is selected
            if (isAnyGroupASelected || isNormalSelected) return;
            dispatch(toggleSymptom(option));
            return;
        }

        // If option is Normal newborn
        if (option === LABEL_NORMAL) {
            // Allow deselect if already selected
            if (isNormalSelected) {
                dispatch(toggleSymptom(option));
                return;
            }
            // Block selecting Normal if Group A or Sick is selected
            if (isAnyGroupASelected || isSickSelected) return;
            dispatch(toggleSymptom(option));
            return;
        }

        // Default fallback (shouldn't hit in current options)
        dispatch(toggleSymptom(option));
    };

    const handleSingleSelect = (value) => {
        dispatch(setPna(value));
    };

    return (
        <ScrollView contentContainerStyle={styles.content}>
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>{current.title}</Text>
                <Text style={styles.sectionDesc}>{current.desc}</Text>
            </View>

            <View style={styles.options}>
                {current.type === 'options' && current.options?.map((opt, idx) => (
                    <TouchableOpacity
                        key={idx}
                        style={styles.checkboxContainer}
                        onPress={() => handleSingleSelect(opt.value)}
                    >
                        <View style={[
                            globalStyles.checkboxBox,
                            Number(registerCriteria.pna) === Number(opt.value) && globalStyles.checkboxChecked
                        ]}>
                            {Number(registerCriteria.pna) === Number(opt.value) && (
                                <Text style={globalStyles.checkmark}>✓</Text>
                            )}
                        </View>
                        <Text style={styles.multiOptionText}>{opt.label}</Text>
                    </TouchableOpacity>
                ))}

                {current.type === 'multi' && current.options?.map((item, idx) => {
                    const isItemSelected = selectedSymptoms.includes(item);
                    let disabled = false;
                    if (GROUP_A.includes(item)) {
                        // Disable GA/BW/HIE if Sick or Normal is selected (but allow deselect of the selected one)
                        disabled = !isItemSelected && (isSickSelected || isNormalSelected);
                    } else if (item === LABEL_SICK) {
                        // Disable Sick if Group A is selected or Normal is selected (but allow deselect)
                        disabled = !isItemSelected && (isAnyGroupASelected || isNormalSelected);
                    } else if (item === LABEL_NORMAL) {
                        // Disable Normal if Group A is selected or Sick is selected (but allow deselect)
                        disabled = !isItemSelected && (isAnyGroupASelected || isSickSelected);
                    }

                    return (
                        <TouchableOpacity
                            key={idx}
                            style={[styles.checkboxContainer, disabled && { opacity: 0.5 }]}
                            onPress={() => !disabled && handleMultiSelect(item)}
                            disabled={disabled}
                        >
                            <View style={[
                                globalStyles.checkboxBox,
                                isItemSelected && globalStyles.checkboxChecked
                            ]}>
                                {isItemSelected && (
                                    <Text style={globalStyles.checkmark}>✓</Text>
                                )}
                            </View>
                            <Text style={styles.multiOptionText}>{item}</Text>
                        </TouchableOpacity>
                    );
                })}
            </View>

            <View style={styles.footerButtons}>
                <TouchableOpacity style={styles.nextButton} onPress={onNext}>
                    <Text style={styles.nextButtonText}>{t.t('Next')}</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
};


export default MainContent


const styles = StyleSheet.create({

    content: {
        padding: responsive(20),
        flexGrow: 1,
        justifyContent: 'flex-start',
        backgroundColor: colors.background.white
    },
    section: {
        marginBottom: 10,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 5,
        paddingBottom: responsive(5),
        borderBottomWidth: 1,
        borderBottomColor: colors.border.headingText
    },
    sectionDesc: {
        fontWeight: '600',
        paddingTop: 5,
        fontSize: 15,
        color: '#333',
    },


    options: {
        marginBottom: 30,
    },
    optionButton: {
        padding: responsive(15),
        marginBottom: 12,
        borderRadius: 6,
        borderWidth: 1,
        borderColor: colors.border.gray,
        backgroundColor: '#fff',
    },
    optionSelected: {
        backgroundColor: '#7FC5C6',
    },
    optionText: {
        fontWeight: '400',
        fontSize: 16,
        textAlign: 'center',
    },
    optionTextSelected: {
        color: '#fff',
    },


    nextButton: {
        backgroundColor: colors.button.primary,
        paddingVertical: responsive(14),
        borderRadius: 30,
    },
    nextButtonText: {
        color: colors.text.white,
        textAlign: 'center',
        fontSize: 16,
    },
    inputBox: {
        borderWidth: 1,
        borderColor: colors.border.gray,
        padding: 14,
        borderRadius: 8,
        fontSize: 16,
        backgroundColor: '#fff',
    },



    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    checkboxBox: {
        height: 20,
        width: 20,
        borderRadius: 5,
        borderWidth: 2,
        borderColor: '#7FC5C6',
        backgroundColor: '#fff',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
    },
    checkboxChecked: {
        backgroundColor: '#7FC5C6',
    },
    checkmark: {
        color: '#fff',
        fontSize: 14,
        fontWeight: 'bold',
        marginTop: -1,
    },
    multiOptionText: {
        fontSize: 16,
        color: '#000',
    },


});