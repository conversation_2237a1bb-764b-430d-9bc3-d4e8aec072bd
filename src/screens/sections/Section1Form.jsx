import React, { useState, useEffect, useMemo, useContext } from 'react';
import { Text, View, TouchableOpacity, StyleSheet, TextInput, Alert } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { saveSectionThunk, updateSectionProgressThunk } from '../../features/sections/sectionSlice';
import { useLocalSearchParams, useRouter } from 'expo-router';
import ScreenWrapper from '../../components/ScreenWrapper';
import CommonHeader from '../../components/CommonHeader';
import CommonFooter from '../../components/CommonFooter';
import LanguageContext from '../../context/LanguageContext';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { MaterialIcons } from '@expo/vector-icons';
import CommonDropdown from '../../components/CommonDropdown';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Section1Schema, section1DefaultValues } from '../../schemas/section1Schema';
import ROUTES from '../../config/routes'
import colors from '../../theme/colors';
import { getMonthOptions, getYearOptionsRange, getDayOptions } from '../../utils/dateUtils';
import { mapSection1ToPayload } from '../../helpers/sections/section1';
import { fetchHospitalsThunk } from '../../features/register/registerSlice';

const Section1Form = () => {

    const dispatch = useDispatch();
    const {
        hospitals: allHospitals,
        hospitalsLoading,
    } = useSelector((state) => state.register);
    const {
        loading
    } = useSelector((state) => state.sections);

    const { TNR, hospital } = useLocalSearchParams();
    const router = useRouter();
    const { t } = useContext(LanguageContext);

    const hospitalOptions = useMemo(() => {
        if (!allHospitals?.length) return [];
        const hospitals = allHospitals.map(hospital => ({
            label: `${hospital.hospital_name} (${hospital.hospital_number})`,
            value: hospital.hospital_number,
            name: hospital.hospital_name,
            code: hospital.hospital_number
        }));
        // prepend heading row
        return [{ header: true, label: 'Select Hospital' }, ...hospitals];
    }, [allHospitals]);

    const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm({
        resolver: zodResolver(Section1Schema),
        defaultValues: section1DefaultValues,
    });

    // Clear field functions
    const clearField = (field) => setValue(field, null, { shouldValidate: true });

    const clearAdmissionType = () => {
        setValue('admission_type', null, { shouldValidate: true });
        setValue('inborn_transfer_type', null, { shouldValidate: true });
        setValue('outborn_transfer_type', null, { shouldValidate: true });
        setValue('tnr_hospital_value', '', { shouldValidate: true });
        setValue('tnr_hospital_name', '', { shouldValidate: true });
        setValue('tnr_hospital_code', '', { shouldValidate: true });
        setValue('non_tnr_hospital_name', '', { shouldValidate: true });
    };

    const clearAdmissionInfo = () => {
        // Clear admission date
        setValue('admission_date', '', { shouldValidate: true });
        setAdmDay(null);
        setAdmMonth(null);
        setAdmYear(null);

        // Clear discharge date
        setValue('discharge_date', '', { shouldValidate: true });
        setDisDay(null);
        setDisMonth(null);
        setDisYear(null);

        // Clear admitted to ward
        setValue('admitted_to', '', { shouldValidate: true });
    };

    const clearDischargeStatus = () => {
        setValue('discharge_status', '', { shouldValidate: true });
    };

    const [admDay, setAdmDay] = useState('');
    const [admMonth, setAdmMonth] = useState('');
    const [admYear, setAdmYear] = useState('');
    // Discharge date dropdown state
    const [disDay, setDisDay] = useState('');
    const [disMonth, setDisMonth] = useState('');
    const [disYear, setDisYear] = useState('');
    // Options for ward dropdown
    const wardOptions = [
        { label: t.t('เลือกแผนก'), value: 'header', header: true },
        { label: t.t('NICU'), value: 'nicu' },
        { label: t.t('Sick newborn'), value: 'sick_newborn' },
        { label: t.t('Other'), value: 'other' },
    ];
    // Fetch hospitals on component mount if not already fetched
    useEffect(() => {
        dispatch(fetchHospitalsThunk());
    }, []);

    const onSubmit = async (data) => {
        try {
            const TNRPatient = TNR || '';
            const hospitalPatient = hospital || '';
            const formData = mapSection1ToPayload(data, {
                currentHospital: hospitalPatient,
                TNRPatient: TNRPatient
            });

            await dispatch(saveSectionThunk({
                num: "1",
                formData: formData.data,
            })).unwrap();
            // Update section progress after successful save
            await dispatch(updateSectionProgressThunk({
                TNR: TNRPatient,
                num: "1"
            })).unwrap();

            Alert.alert("Success", "Section 1 data saved successfully!");
        } catch (error) {
            console.error('Error saving section 1:', error);
            Alert.alert("Error", error.message || "Failed to save section 1 data");
        }
    };

    // Recursively collect and log react-hook-form errors in a readable list
    const logFormErrors = (errs) => {
        const entries = [];
        const walk = (obj, path = []) => {
            if (!obj || typeof obj !== 'object') return;
            Object.entries(obj).forEach(([key, val]) => {
                const nextPath = [...path, key];
                if (val && typeof val === 'object') {
                    if (val.message) {
                        entries.push({ name: nextPath.join('.'), message: val.message });
                    }
                    // Dive deeper for nested objects/arrays
                    walk(val, nextPath);
                }
            });
        };
        walk(errs);
        return entries;
    };

    // Invalid submit handler for react-hook-form
    const onInvalid = (errs) => {
        logFormErrors(errs);
    };

    const handleBack = () => {
        router.replace(ROUTES.PRIVATE.PATIENT_DISPLAY_SCREEN)
    };

    const admissionType = watch('admission_type');
    const admissionDate = watch('admission_date');
    const dischargeDate = watch('discharge_date');
    const hospitalStay = watch('hospital_stay');

    useEffect(() => {
        if (admissionDate && dischargeDate) {
            const start = new Date(admissionDate);
            const end = new Date(dischargeDate);
            const diffTime = Math.abs(end - start);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            setValue('hospital_stay', `${diffDays} Day(s)`);
        } else {
            setValue('hospital_stay', 'N/A Day(s)');
        }
    }, [admissionDate, dischargeDate, setValue]);

    return (
        <ScreenWrapper topColor={colors.header.background}>
            <CommonHeader title="Section 1" onBackPress={handleBack} />
            <KeyboardAwareScrollView
                style={{ flex: 1, backgroundColor: colors.white }}
                contentContainerStyle={styles.scrollContainer}
                keyboardShouldPersistTaps="handled"
            >
                <View style={styles.subHeadingContainer}>
                    <View style={styles.subHeader}>
                        <Text style={styles.subHeaderText}>Section 1: Admission data</Text>
                        <TouchableOpacity onPress={clearAdmissionType}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <Controller
                    control={control}
                    name="admission_type"
                    render={({ field: { onChange, value } }) => (
                        <View>
                            <TouchableOpacity style={[styles.radioContainer]} onPress={() => onChange('inborn')}>
                                <View style={styles.radioButton}>
                                    {value === 'inborn' && <View style={styles.radioButtonSelected} />}
                                </View>
                                <Text>{t.t('Inborn')}</Text>
                            </TouchableOpacity>
                            {admissionType === 'inborn' && (
                                <View style={[styles.subRadioGroup]}>
                                    <View style={styles.subCard}>
                                        <View style={styles.subTitleRow}>
                                            <Text style={styles.subTitleText}>{t.t('Intrauterine transfer')}</Text>
                                            <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                                        </View>
                                        <Controller
                                            control={control}
                                            name="inborn_transfer_type"
                                            render={({ field: { onChange, value } }) => (
                                                <View>
                                                    <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('intrauterine')}>
                                                        <View style={styles.radioButton}>
                                                            {value === 'intrauterine' && <View style={styles.radioButtonSelected} />}
                                                        </View>
                                                        <Text>{t.t('Yes')}</Text>
                                                    </TouchableOpacity>
                                                    <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('not_intrauterine')}>
                                                        <View style={styles.radioButton}>
                                                            {value === 'not_intrauterine' && <View style={styles.radioButtonSelected} />}
                                                        </View>
                                                        <Text>{t.t('No')}</Text>
                                                    </TouchableOpacity>
                                                </View>
                                            )}
                                        />
                                        {errors.inborn_transfer_type && (
                                            <Text style={styles.errorText}>{errors.inborn_transfer_type.message}</Text>
                                        )}
                                    </View>
                                </View>
                            )}

                            <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('outborn')}>
                                <View style={styles.radioButton}>
                                    {value === 'outborn' && <View style={styles.radioButtonSelected} />}
                                </View>
                                <Text>{t.t('Outborn / Transfer from another hospital')}</Text>
                            </TouchableOpacity>
                            {admissionType === 'outborn' && (
                                <View style={[styles.subRadioGroup, styles.subCard]}>
                                    <Controller
                                        control={control}
                                        name="outborn_transfer_type"
                                        render={({ field: { onChange, value } }) => (
                                            <View style={styles.radioGroup}>
                                                {/* TNR option with inline dropdown */}
                                                <View style={styles.optionBlock}>
                                                    <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('tnr')}>
                                                        <View style={styles.radioButton}>
                                                            {value === 'tnr' && <View style={styles.radioButtonSelected} />}
                                                        </View>
                                                        <Text>{t.t('TNR member hospital')}</Text>
                                                    </TouchableOpacity>
                                                    {value === 'tnr' && (
                                                        <View style={styles.optionInputIndent}>
                                                            <Controller
                                                                control={control}
                                                                name="tnr_hospital_value" // this field keeps the dropdown in sync (stores item.value)
                                                                rules={{ shouldUnregister: false }}
                                                                render={({ field: { value, onChange } }) => (
                                                                    <CommonDropdown
                                                                        data={hospitalOptions}
                                                                        value={value}
                                                                        headerValue="Select Hospital"
                                                                        placeholder={hospitalsLoading ? t.t('Loading...') : t.t('Select hospital')}
                                                                        style={[styles.input]}
                                                                        onChange={(item) => {
                                                                            if (!item || item.header || item.disable) {
                                                                                onChange(item.label);
                                                                                setValue('tnr_hospital_name', item.name, { shouldValidate: true });
                                                                                setValue('tnr_hospital_code', item.code, { shouldValidate: true });
                                                                                return;
                                                                            }
                                                                            onChange(item.value);
                                                                            setValue('tnr_hospital_name', item.name, { shouldValidate: true });
                                                                            setValue('tnr_hospital_code', item.code, { shouldValidate: true });
                                                                        }}
                                                                    />
                                                                )}
                                                            />

                                                            {errors.tnr_hospital_name && <Text style={styles.errorText}>{errors.tnr_hospital_name.message}</Text>}
                                                        </View>
                                                    )}
                                                </View>

                                                {/* Non TNR option with inline text input */}
                                                <View style={styles.optionBlock}>
                                                    <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('non_tnr')}>
                                                        <View style={styles.radioButton}>
                                                            {value === 'non_tnr' && <View style={styles.radioButtonSelected} />}
                                                        </View>
                                                        <Text>{t.t('Non TNR member hospital')}</Text>
                                                    </TouchableOpacity>
                                                    {value === 'non_tnr' && (
                                                        <View style={styles.optionInputIndent}>
                                                            <Controller
                                                                control={control}
                                                                name="non_tnr_hospital_name"
                                                                rules={{ shouldUnregister: false }}
                                                                render={({ field: { onChange: onTextChange, onBlur, value: textValue } }) => (
                                                                    <TextInput
                                                                        style={styles.input}
                                                                        onBlur={onBlur}
                                                                        onChangeText={onTextChange}
                                                                        value={textValue}
                                                                        placeholder="ชื่อโรงพยาบาล"
                                                                    />
                                                                )}
                                                            />
                                                            {errors.non_tnr_hospital_name && <Text style={styles.errorText}>{errors.non_tnr_hospital_name.message}</Text>}
                                                        </View>
                                                    )}
                                                </View>
                                            </View>
                                        )}
                                    />
                                    {errors.outborn_transfer_type && <Text style={styles.errorText}>{errors.outborn_transfer_type.message}</Text>}
                                </View>
                            )}
                            <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('bba')}>
                                <View style={styles.radioButton}>
                                    {value === 'bba' && <View style={styles.radioButtonSelected} />}
                                </View>
                                <Text>BBA</Text>
                            </TouchableOpacity>
                        </View>
                    )}
                />
                {errors.admission_type && <Text style={styles.errorText}>{errors.admission_type.message}</Text>}


                <View style={styles.subHeadingContainer}>
                    <View style={styles.subHeader}>
                        <Text style={styles.subHeaderText}>Admission Info</Text>
                        <TouchableOpacity onPress={clearAdmissionInfo}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                <View style={styles.inputContainer}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={styles.label}>{t.t('Admit to ward')}</Text>
                        <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                    </View>
                    <View>
                        <Controller
                            control={control}
                            name="admitted_to"
                            render={({ field: { onChange, value } }) => {
                                const selectedVal = wardOptions.find(o => o.label === value)?.value ?? null;
                                return (
                                    <CommonDropdown
                                        data={wardOptions}
                                        value={selectedVal}
                                        placeholder={t.t('Select ward')}
                                        style={[styles.input]}
                                        onChange={(item) => {
                                            if (!item || item.header || item.disable) {
                                                onChange('');
                                                return;
                                            }
                                            onChange(item.label);
                                        }}
                                    />
                                );
                            }}
                        />
                    </View>
                    {errors.admitted_to && <Text style={styles.errorText}>{errors.admitted_to.message}</Text>}
                </View>

                <View style={styles.inputContainer}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={styles.label}>{t.t('Date of Admission in our center')}</Text>
                        <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                    </View>
                    <View style={{ flexDirection: 'row' }}>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Day'), value: 'header', header: true }, ...getDayOptions(admYear, admMonth)]}
                                value={admDay}
                                placeholder={t.t('Day')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Day')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setAdmDay(null);
                                        setValue('admission_date', '');
                                        return;
                                    }
                                    setAdmDay(item.value);
                                    const yy = (admYear || '').toString().padStart(4, '');
                                    const mm = (admMonth || '').toString().padStart(2, '');
                                    const dd = (item.value || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('admission_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    }
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Month'), value: 'header', header: true }, ...getMonthOptions()]}
                                value={admMonth}
                                placeholder={t.t('Month')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Month')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setAdmMonth(null);
                                        setAdmDay(null);
                                        setValue('admission_date', '');
                                        return;
                                    }
                                    const newMonth = item.value;
                                    setAdmMonth(newMonth);

                                    // Reset day if it's invalid for the new month
                                    const maxDaysLen = getDayOptions(admYear, newMonth).length;
                                    if (admDay && parseInt(admDay, 10) > maxDaysLen) {
                                        setAdmDay(null);
                                    }

                                    // Update the date if we have all components
                                    const yy = (admYear || '').toString().padStart(4, '');
                                    const mm = (newMonth || '').toString().padStart(2, '');
                                    const dd = (admDay || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('admission_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    } else {
                                        setValue('admission_date', '');
                                    }
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Year'), value: 'header', header: true }, ...getYearOptionsRange(4, 10)]}
                                value={admYear}
                                placeholder={t.t('Year')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Year')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setAdmYear(null);
                                        setAdmDay(null);  // Reset day since it might be invalid without a year
                                        setValue('admission_date', '');
                                        return;
                                    }

                                    const newYear = item.value;
                                    setAdmYear(newYear);

                                    // Reset day if it's invalid for the new year/month
                                    const maxDaysLen = getDayOptions(newYear, admMonth).length;
                                    if (admDay && parseInt(admDay, 10) > maxDaysLen) {
                                        setAdmDay(null);
                                    }

                                    // Update the date if we have all components
                                    const yy = (newYear || '').toString().padStart(4, '');
                                    const mm = (admMonth || '').toString().padStart(2, '');
                                    const dd = (admDay || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('admission_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    } else {
                                        setValue('admission_date', '');
                                    }
                                }}
                            />
                        </View>
                    </View>
                    {errors.admission_date && <Text style={styles.errorText}>{errors.admission_date.message}</Text>}
                </View>

                <View style={styles.inputContainer}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={styles.label}>{t.t('Date of Discharge')}</Text>
                        <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                    </View>
                    <View style={{ flexDirection: 'row' }}>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Day'), value: 'header', header: true }, ...getDayOptions(disYear, disMonth)]}
                                value={disDay}
                                placeholder={t.t('Day')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Day')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setDisDay(null);
                                        setValue('discharge_date', '');
                                        return;
                                    }
                                    setDisDay(item.value);
                                    const yy = (disYear || '').toString().padStart(4, '');
                                    const mm = (disMonth || '').toString().padStart(2, '');
                                    const dd = (item.value || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('discharge_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    }
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Month'), value: 'header', header: true }, ...getMonthOptions()]}
                                value={disMonth}
                                placeholder={t.t('Month')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Month')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setDisMonth(null);
                                        setDisDay(null);
                                        setValue('discharge_date', '');
                                        return;
                                    }

                                    const newMonth = item.value;
                                    setDisMonth(newMonth);

                                    // Reset day if it's invalid for the new month
                                    const maxDaysLen = getDayOptions(disYear, newMonth).length;
                                    if (disDay && parseInt(disDay, 10) > maxDaysLen) {
                                        setDisDay(null);
                                    }

                                    // Update the date if we have all components
                                    const yy = (disYear || '').toString().padStart(4, '');
                                    const mm = (newMonth || '').toString().padStart(2, '');
                                    const dd = (disDay || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('discharge_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    } else {
                                        setValue('discharge_date', '');
                                    }
                                }}
                            />
                        </View>
                        <View style={{ flex: 1 }}>
                            <CommonDropdown
                                data={[{ label: t.t('Year'), value: 'header', header: true }, ...getYearOptionsRange(4, 10)]}
                                value={disYear}
                                placeholder={t.t('Year')}
                                style={[styles.input]}
                                search={false}
                                headerValue={t.t('Year')}
                                onChange={(item) => {
                                    if (!item || item.header) {
                                        setDisYear(null);
                                        setDisDay(null);  // Reset day since it might be invalid without a year
                                        setValue('discharge_date', '');
                                        return;
                                    }

                                    const newYear = item.value;
                                    setDisYear(newYear);

                                    // Reset day if it's invalid for the new year/month
                                    const maxDaysLen = getDayOptions(newYear, disMonth).length;
                                    if (disDay && parseInt(disDay, 10) > maxDaysLen) {
                                        setDisDay(null);
                                    }

                                    // Update the date if we have all components
                                    const yy = (newYear || '').toString().padStart(4, '');
                                    const mm = (disMonth || '').toString().padStart(2, '');
                                    const dd = (disDay || '').toString().padStart(2, '');
                                    if (yy && mm && dd) {
                                        setValue('discharge_date', `${yy}-${mm}-${dd}`, { shouldValidate: true });
                                    } else {
                                        setValue('discharge_date', '');
                                    }
                                }}
                            />
                        </View>
                    </View>
                    {errors.discharge_date && <Text style={styles.errorText}>{errors.discharge_date.message}</Text>}
                </View>

                <View style={styles.subHeadingContainer}>
                    <View style={styles.subHeader}>
                        <Text style={styles.subHeaderText}>Discharge from our center info</Text>
                        <TouchableOpacity onPress={clearDischargeStatus}>
                            <Text style={styles.clearText}>{t.t('Clear')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                <View style={{ flexDirection: 'row', alignItems: 'center', paddingLeft: 16, }}>
                    <Text style={styles.label}>{t.t('Status at discharge from our center')}</Text>
                    <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                </View>
                <Controller
                    control={control}
                    name="discharge_status"
                    render={({ field: { onChange, value } }) => (
                        <View style={styles.radioGroup}>
                            {/* <View style={styles.optionBlock}> */}
                            <View style={styles.optionBlock}>
                                <TouchableOpacity style={[styles.radioRow]} onPress={() => onChange('refer_tnr')}>
                                    <View style={styles.radioButton}>
                                        {value === 'refer_tnr' && <View style={styles.radioButtonSelected} />}
                                    </View>
                                    <Text>{t.t('Refer to TNR member hospital')}</Text>
                                </TouchableOpacity>
                                <Text style={styles.redNoteNoPad}>(ไม่ต้องทำ section 6)</Text>
                            </View>
                            {value === 'refer_tnr' && (
                                <View style={styles.subCard}>
                                    <View style={styles.subTitleRow}>
                                        <Text style={styles.subTitleText}>{t.t('Hospital')}</Text>
                                        <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                                    </View>
                                    <Controller
                                        control={control}
                                        name="discharge_tnr_hospital_value" // this will store item.value (hospital_code)
                                        render={({ field: { value, onChange } }) => (
                                            <CommonDropdown
                                                data={hospitalOptions}
                                                value={value}
                                                headerValue="Select Hospital"
                                                placeholder={hospitalsLoading ? t.t('Loading...') : t.t('Select Hospital')}
                                                style={[styles.input]}
                                                onChange={(item) => {
                                                    if (!item || item.header || item.disable) {
                                                        onChange(item.label);
                                                        setValue('discharge_tnr_hospital', item.name, { shouldValidate: true });
                                                        setValue('discharge_tnr_hospital_code', item.code, { shouldValidate: true });
                                                        return;
                                                    }
                                                    onChange(item.value);

                                                    setValue('discharge_tnr_hospital', item.name, { shouldValidate: true });
                                                    setValue('discharge_tnr_hospital_code', item.code, { shouldValidate: true });
                                                }}
                                            />
                                        )}
                                    />
                                </View>

                            )}
                            {errors.discharge_tnr_hospital && (
                                <Text style={styles.errorText}>{errors.discharge_tnr_hospital.message}</Text>
                            )}

                            <View style={styles.optionBlock}>
                                <TouchableOpacity style={[styles.radioRow]} onPress={() => onChange('discharge_home')}>
                                    <View style={styles.radioButton}>
                                        {value === 'discharge_home' && <View style={styles.radioButtonSelected} />}
                                    </View>
                                    <Text>{t.t('Discharge home')}</Text>
                                </TouchableOpacity>
                                <Text style={styles.redNoteNoPad}>(ต้องทำ section 6)</Text>
                            </View>

                            <View style={styles.optionBlock}>
                                <TouchableOpacity style={[styles.radioRow]} onPress={() => onChange('refer_level2')}>
                                    <View style={styles.radioButton}>
                                        {value === 'refer_level2' && <View style={styles.radioButtonSelected} />}
                                    </View>
                                    <Text>{t.t('Refer to level 2 hospital / non TNR member hospital')}</Text>
                                </TouchableOpacity>
                                <Text style={styles.redNoteNoPad}>(ต้องทำ section 6)</Text>
                            </View>
                            {value === 'refer_level2' && (
                                <View style={styles.subCard}>
                                    <View style={styles.subTitleRow}>
                                        <Text style={styles.subTitleText}>{t.t('Hospital')}</Text>
                                        <MaterialIcons name="info-outline" size={16} color="#7FC5C6" style={{ marginLeft: 6 }} />
                                    </View>
                                    <Controller
                                        control={control}
                                        name="discharge_level2_hospital"
                                        render={({ field: { onChange: onTextChange, onBlur, value: textVal } }) => (
                                            <TextInput
                                                style={styles.input}
                                                onBlur={onBlur}
                                                onChangeText={onTextChange}
                                                value={textVal}
                                                placeholder={t.t('Hospital name')}
                                            />
                                        )}
                                    />
                                </View>
                            )}
                            {errors.discharge_level2_hospital && (
                                <Text style={styles.errorText}>{errors.discharge_level2_hospital.message}</Text>
                            )}

                            <View style={styles.optionBlock}>
                                <TouchableOpacity style={[styles.radioRow]} onPress={() => onChange('death')}>
                                    <View style={styles.radioButton}>
                                        {value === 'death' && <View style={styles.radioButtonSelected} />}
                                    </View>
                                    <Text>{t.t('Death')}</Text>
                                </TouchableOpacity>
                                <Text style={styles.redNoteNoPad}>(ต้องทำ section 6)</Text>
                            </View>
                        </View>
                    )}
                />
                {errors.discharge_status && <Text style={styles.errorText}>{errors.discharge_status.message}</Text>}

                <View style={styles.inputContainer}>
                    <View>
                        <Text style={styles.label}>{t.t('Hospital stay in our center')}</Text>
                        <Text style={styles.helperText}>{t.t('The date of calculation will be updated daily')}</Text>
                    </View>
                    <Text style={styles.datePicker}>{hospitalStay || '1 Day(s)'}</Text>
                </View>
            </KeyboardAwareScrollView>
            <CommonFooter onSubmit={handleSubmit(onSubmit, onInvalid)} buttonText={t.t('Save')} loading={loading} />
            {/* <CommonFooter onSubmit={trySubmit} buttonText={t.t('Save')} /> */}
        </ScreenWrapper>
    );
};

const styles = StyleSheet.create({
    scrollContainer: {
        paddingTop: 20,
    },
    subHeadingContainer: {
        marginBottom: 20,
        paddingLeft: 16,
    },
    subHeader: {
        borderBottomWidth: 1,
        borderBottomColor: colors.border.gray,
        paddingBottom: 3,
        flexDirection: "row",
        justifyContent: "space-between"
    },
    subHeaderText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    radioGroup: {},
    subRadioGroup: {
        // marginLeft: 20,
    },
    pickerInput: {
        justifyContent: 'center', // vertical center
        alignItems: 'flex-start', // left align content
        paddingHorizontal: 12,
    },
    radioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
        paddingLeft: 16
    },
    radioButton: {
        height: 24,
        width: 24,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#7FC5C6',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
    },
    radioButtonSelected: {
        height: 12,
        width: 12,
        borderRadius: 6,
        backgroundColor: '#7FC5C6',
    },
    subCard: {
        backgroundColor: '#F5F5F5',
        paddingLeft: 16,
        paddingVertical: 4,
        paddingRight: 10
    },
    subTitleRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        paddingLeft: 10
    },
    subTitleText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    admissionBlock: {
        paddingVertical: 0,
        marginBottom: 8,
    },
    optionBlock: {
        // marginTop: 6,

    },
    optionInputIndent: {
        paddingLeft: 28,
        marginTop: 6,
    },
    inbornRadio: {
        marginBottom: 2,
    },
    datePicker: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: colors.border.gray,
        padding: 15,
        borderRadius: 5,
        marginBottom: 20,
    },
    label: {
        fontSize: 16,
        marginBottom: 5,
        fontWeight: 'bold',
    },
    button: {
        backgroundColor: colors.button.primary,
        padding: 15,
        borderRadius: 25,
        alignItems: 'center',
        marginTop: 20,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    helperText: {
        fontSize: 12,
        color: '#666',
        marginBottom: 20,
    },
    redNote: {
        color: 'red',
        fontSize: 12,
        paddingLeft: 50,
        marginBottom: 8,
    },
    optionBlock: {
        paddingLeft: 16,
        marginBottom: 8,
    },
    radioRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 0,
    },
    redNoteNoPad: {
        color: 'red',
        fontSize: 12,
        marginTop: 2,
        paddingLeft: 30
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginLeft: 10,
        marginTop: 2,
    },
    inputContainer: {
        marginBottom: 15,
        paddingLeft: 16,
        paddingRight: 16,
    },
    labelWithClear: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    clearText: {
        fontSize: 14,
        color: colors.button?.primary || '#1E88E5',
        fontWeight: '600',
        paddingRight: 9
    },
    dropdown: {
        height: 50,
        borderColor: 'gray',
        borderWidth: 0.5,
        borderRadius: 8,
        paddingHorizontal: 8,
        marginBottom: 10,
        backgroundColor: "white"
    },
    placeholderStyle: {
        fontSize: 16,
    },
    selectedTextStyle: {
        fontSize: 16,
    },
    iconStyle: {
        width: 20,
        height: 20,
    },
    inputSearchStyle: {
        height: 40,
        fontSize: 16,
    },
    input: {
        height: 50,
        borderColor: 'gray',
        borderWidth: 0.5,
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingRight: 16,
        marginBottom: 10,
        backgroundColor: "white"
    },
    dropdownHeaderItemContainer: {
        backgroundColor: '#F7FAFA',
        paddingVertical: 4,
        paddingLeft: 10,
    },
    dropdownHeaderItemText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    dropdownItemContainer: {
        paddingVertical: 4,
        paddingLeft: 10,
    },
    dropdownItemText: {
        fontSize: 16,
    },
});

export default Section1Form;
