import React, { useState, useEffect, useContext } from 'react';
import { Text, View, TouchableOpacity, StyleSheet, TextInput, Alert } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

import ScreenWrapper from '../../components/ScreenWrapper';
import CommonHeader from '../../components/CommonHeader';
import LanguageContext from '../../context/LanguageContext';
import { Section2Schema, section2DefaultValues } from '../../schemas/section2Schema';
import ROUTES from '../../config/routes';
import colors from '../../theme/colors';
import globalStyles from '../../styles/globalStyles';
import CommonFooter from '../../components/CommonFooter';
import mapSection2Data from '../../helpers/sections/section2';
import { useDispatch, useSelector } from 'react-redux';
import { saveSectionThunk, updateSectionProgressThunk } from '../../features/sections/sectionSlice';

const Section2Form = () => {
    const router = useRouter();
    const { t } = useContext(LanguageContext);
    const { loading } = useSelector((state) => state.sections)
    const { control, handleSubmit, watch, setValue, getValues, trigger, clearErrors, formState: { errors } } = useForm({
        resolver: zodResolver(Section2Schema),
        defaultValues: section2DefaultValues,
    });
    const dispatch = useDispatch()
    const { name } = useSelector((state) => state.home)
    const { TNR, hospital } = useLocalSearchParams();
    const onError = (errors) => {
        // You can also show these errors in an alert or toast
        // Alert.alert('Validation Error', 'Please check the form for errors');
    };

    const onSubmit = async (data) => {
        try {
            const payload = mapSection2Data(data, name, TNR, hospital);
            // c
            await dispatch(saveSectionThunk({
                num: "2",
                formData: payload.data
            })).unwrap();

            // Update section progress after successful save
            await dispatch(updateSectionProgressThunk({
                TNR,
                num: "2"
            })).unwrap();

            Alert.alert("Success", "Section 2 data saved successfully!");
        } catch (error) {
            console.error('Error submitting form:', error);
            Alert.alert("Error", error.message || "Failed to save section 2 data");
        }
    };

    const handleBack = () => {
        router.back();
    };

    const maternalAgeType = watch('maternal_age_type');
    const abnormalSerology = watch('abnormal_serology');
    const complicationsPregnancy = watch('complications_pregnancy');
    const multipleGestationType = watch('multiple_gestation_type');
    const intrapartumComplications = watch('intrapartum_complications');
    // const gbsScreeningStatus = watch('gbs_screening');
    const gbsScreening = watch('gbs_screening')
    const maternalMedication = watch('maternal_medication') || [];
    const [otherMedicationFields, setOtherMedicationFields] = useState([{ id: 0, value: '' }]);

    const addOtherMedicationField = () => {
        const newId = otherMedicationFields.length > 0
            ? Math.max(...otherMedicationFields.map(field => field.id)) + 1
            : 0;
        setOtherMedicationFields([...otherMedicationFields, { id: newId, value: '' }]);
    };

    const removeOtherMedicationField = (id) => {
        if (otherMedicationFields.length > 1) {
            setOtherMedicationFields(otherMedicationFields.filter(field => field.id !== id));
            // Update form value when removing a field
            const currentValues = getValues('maternal_medication_other') || [];
            const updatedValues = currentValues.filter((_, index) =>
                otherMedicationFields.findIndex(f => f.id === id) !== index
            );
            setValue('maternal_medication_other', updatedValues, { shouldValidate: true });
        }
    };

    const updateOtherMedicationValue = (id, value) => {
        const updatedFields = otherMedicationFields.map(field =>
            field.id === id ? { ...field, value } : field
        );
        setOtherMedicationFields(updatedFields);

        // Update form values
        const values = updatedFields.map(field => field.value).filter(Boolean);
        setValue('maternal_medication_other', values, { shouldValidate: true });
    };


    // const toggleCheckbox = (field, value) => {
    //     const currentValues = getValues(field) || [];
    //     let newValues;

    //     if (currentValues.includes(value)) {
    //         // unselect
    //         newValues = currentValues.filter(v => v !== value);
    //     } else {
    //         // select
    //         newValues = [...currentValues, value];
    //     }

    //     // Exclusive logic for abnormal_serology
    //     if (field === "abnormal_serology") {
    //         if (value === "normal_all" && !currentValues.includes("normal_all")) {
    //             newValues = ["normal_all"];
    //         } else if (value === "na" && !currentValues.includes("na")) {
    //             newValues = ["na"];
    //         } else {
    //             newValues = newValues.filter(v => v !== "normal_all" && v !== "na");
    //         }
    //     }
    //     if (field === "complications_pregnancy") {
    //         if (value === "no_complication" && !currentValues.includes("no_complication")) {
    //             // If user selects No complication → only that option remains
    //             newValues = ["no_complication"];
    //         } else if (value === "na" && !currentValues.includes("na")) {
    //             // If user selects N/A → only N/A remains
    //             newValues = ["na"];
    //         } else {
    //             // If selecting anything else → remove no_complication & na
    //             newValues = newValues.filter(v => v !== "no_complication" && v !== "na");
    //         }
    //     }
    //     if (field === "intrapartum_complications") {
    //         if (value === "no_complication" && !currentValues.includes("no_complication")) {
    //             // Only No complication
    //             newValues = ["no_complication"];
    //         } else if (value === "na" && !currentValues.includes("na")) {
    //             // Only N/A
    //             newValues = ["na"];
    //         } else {
    //             // Remove exclusive values if selecting others
    //             newValues = newValues.filter(v => v !== "no_complication" && v !== "na");
    //         }
    //     }
    //     if (field === "gbs_screening") {
    //         if (value === "na" && !currentValues.includes("na")) {
    //             // If N/A selected → only keep N/A
    //             newValues = ["na"];
    //         } else if (value === "not_done" && !currentValues.includes("not_done")) {
    //             // If Not Done selected → only keep Not Done
    //             newValues = ["not_done"];
    //         } else {
    //             // If selecting other values → remove N/A and Not Done
    //             newValues = newValues.filter(v => v !== "na" && v !== "not_done");
    //         }
    //     }
    //     if (field === "maternal_medication") {

    //         if (value === "na" && !currentValues.includes("na")) {
    //             // If N/A selected → only keep N/A
    //             newValues = ["na"];
    //         }
    //         else if (value === "no_medication") {
    //             if (!currentValues.includes("no_medication")) {
    //                 // Selecting "No medication" clears all others
    //                 newValues = ["no_medication"];
    //             } else {
    //                 // Unselect "No medication"
    //                 newValues = [];
    //             }
    //         } else {
    //             // If selecting anything else, remove "no_medication"
    //             newValues = newValues.filter(v => v !== "no_medication");

    //             if (currentValues.includes(value)) {
    //                 newValues = newValues.filter(v => v !== value);
    //             } else {
    //                 newValues.push(value);
    //             }
    //         }
    //     }
    //     setValue(field, newValues, { shouldValidate: true });

    //     trigger();
    // };
    const toggleCheckbox = (field, value) => {
  const currentValues = getValues(field) || [];
  let newValues = currentValues.includes(value)
    ? currentValues.filter(v => v !== value)
    : [...currentValues, value];

  const exclusiveRules = {
    abnormal_serology: ["normal_all", "na"],
    complications_pregnancy: ["no_complication", "na"],
    intrapartum_complications: ["no_complication", "na"],
    maternal_medication: ["no_medication", "na"],
  };

  if (exclusiveRules[field]) {
    const exclusives = exclusiveRules[field];
    if (exclusives.includes(value) && !currentValues.includes(value)) {
      newValues = [value]; // keep only the exclusive
    } else {
      newValues = newValues.filter(v => !exclusives.includes(v));
    }
  }

  // Special GBS logic
  if (field === "gbs_screening") {
    if ((value === "na" || value === "not_done") && !currentValues.includes(value)) {
      newValues = [value]; // exclusive keep only one
    } else {
      newValues = newValues.filter(v => v !== "na" && v !== "not_done");

      // Positive vs Negative mutually exclusive
      if (value === "positive" && !currentValues.includes("positive")) {
        newValues = newValues.filter(v => v !== "negative");
      }
      if (value === "negative" && !currentValues.includes("negative")) {
        newValues = newValues.filter(v => v !== "positive");
      }
    }
  }

  setValue(field, newValues, { shouldValidate: true });
  trigger();
};


    return (
        <ScreenWrapper topColor={colors.header.background}>
            <CommonHeader title='Section 2' onBackPress={handleBack} />
            <KeyboardAwareScrollView
                style={{ flex: 1, backgroundColor: colors.white }}
            >
                <View style={styles.scrollContainer}>
                    <View style={styles.subHeadingContainer}>
                        <Text style={styles.subHeader}>Section 2: Maternal Data</Text>
                    </View>

                    {/* Maternal Age */}
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>{t.t('Maternal age')}</Text>
                        <Text style={styles.sublabel}>{t.t("If you don't know, choose N/A")}</Text>

                        <Controller
                            control={control}
                            name="maternal_age_type"
                            render={({ field: { onChange, value: ageType } }) => (
                                <View>
                                    <Controller
                                        control={control}
                                        name="maternal_age"
                                        render={({ field: { onChange, onBlur, value } }) => (
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    ageType === 'na' && { backgroundColor: '#f0f0f0' }
                                                ]}
                                                onBlur={onBlur}
                                                onChangeText={onChange}
                                                value={value}
                                                placeholder={t.t('Year')}
                                                keyboardType="numeric"
                                                editable={ageType !== 'na'}
                                            />
                                        )}
                                    />

                                    <TouchableOpacity
                                        style={styles.checkboxContainer}
                                        onPress={() => {
                                            if (ageType === 'na') {
                                                // unselect N/A
                                                onChange('');
                                            } else {
                                                // select N/A and clear maternal_age
                                                onChange('na');
                                                setValue('maternal_age', '', { shouldValidate: true });
                                            }
                                        }}
                                    >
                                        <View
                                            style={[
                                                globalStyles.checkboxBox,
                                                ageType === 'na' && globalStyles.checkboxChecked
                                            ]}
                                        >
                                            {ageType === 'na' && <Text style={globalStyles.checkmark}>✓</Text>}
                                        </View>
                                        <Text style={{ marginLeft: 8 }}>N/A</Text>
                                    </TouchableOpacity>
                                </View>
                            )}
                        />

                        {errors.maternal_age_type && (
                            <Text style={styles.errorText}>{errors.maternal_age_type.message}</Text>
                        )}
                        {errors.maternal_age && (
                            <Text style={styles.errorText}>{errors.maternal_age.message}</Text>
                        )}
                    </View>



                    {/* GPA */}
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>{t.t('GPA')}</Text>
                        <Text style={styles.sublabel}>{t.t("If you don't know, choose N/A")}</Text>

                        <Controller
                            control={control}
                            name="gpa_type"
                            render={({ field: { onChange, value: gpaType } }) => (
                                <View>
                                    {/* Row with G, P, A fields */}
                                    <View style={styles.rowContainer}>
                                        <View style={styles.gpaField}>
                                            <Text style={styles.inputtext}>G</Text>
                                            <Controller
                                                control={control}
                                                name="gpa_g"
                                                render={({ field: { onChange, onBlur, value } }) => (
                                                    <TextInput
                                                        style={[
                                                            styles.input,
                                                            gpaType === 'na' && { backgroundColor: '#f0f0f0' }
                                                        ]}
                                                        onBlur={onBlur}
                                                        onChangeText={(text) => {
                                                            onChange(text);
                                                            // Set gpa_type to 'g' when user starts typing
                                                            if (text && !gpaType) {
                                                                setValue('gpa_type', 'g', { shouldValidate: true });
                                                            } else if (!text && gpaType === 'g' && !getValues('gpa_p') && !getValues('gpa_a')) {
                                                                // If this is the only field with a value and it's being cleared, clear gpa_type
                                                                setValue('gpa_type', null, { shouldValidate: true });
                                                            }
                                                        }}
                                                        value={value}
                                                        placeholder="G"
                                                        keyboardType="numeric"
                                                        editable={gpaType !== 'na'}
                                                    />
                                                )}
                                            />
                                        </View>

                                        <View style={styles.gpaField}>
                                            <Text style={styles.inputtext}>P</Text>
                                            <Controller
                                                control={control}
                                                name="gpa_p"
                                                render={({ field: { onChange, onBlur, value } }) => (
                                                    <TextInput
                                                        style={[
                                                            styles.input,
                                                            gpaType === 'na' && { backgroundColor: '#f0f0f0' }
                                                        ]}
                                                        onBlur={onBlur}
                                                        onChangeText={(text) => {
                                                            onChange(text);
                                                            // Set gpa_type to 'p' when user starts typing
                                                            if (text && !gpaType) {
                                                                setValue('gpa_type', 'p', { shouldValidate: true });
                                                            } else if (!text && gpaType === 'p' && !getValues('gpa_g') && !getValues('gpa_a')) {
                                                                // If this is the only field with a value and it's being cleared, clear gpa_type
                                                                setValue('gpa_type', null, { shouldValidate: true });
                                                            }
                                                        }}
                                                        value={value}
                                                        placeholder="P"
                                                        keyboardType="numeric"
                                                        editable={gpaType !== 'na'}
                                                    />
                                                )}
                                            />
                                        </View>

                                        <View style={styles.gpaField}>
                                            <Text style={styles.inputtext}>A</Text>
                                            <Controller
                                                control={control}
                                                name="gpa_a"
                                                render={({ field: { onChange, onBlur, value } }) => (
                                                    <TextInput
                                                        style={[
                                                            styles.input,
                                                            gpaType === 'na' && { backgroundColor: '#f0f0f0' }
                                                        ]}
                                                        onBlur={onBlur}
                                                        onChangeText={(text) => {
                                                            onChange(text);
                                                            // Set gpa_type to 'a' when user starts typing
                                                            if (text && !gpaType) {
                                                                setValue('gpa_type', 'a', { shouldValidate: true });
                                                            } else if (!text && gpaType === 'a' && !getValues('gpa_g') && !getValues('gpa_p')) {
                                                                // If this is the only field with a value and it's being cleared, clear gpa_type
                                                                setValue('gpa_type', null, { shouldValidate: true });
                                                            }
                                                        }}
                                                        value={value}
                                                        placeholder="A"
                                                        keyboardType="numeric"
                                                        editable={gpaType !== 'na'}
                                                    />
                                                )}
                                            />
                                        </View>
                                    </View>

                                    {/* N/A Checkbox */}
                                    <TouchableOpacity
                                        style={styles.checkboxContainer}
                                        onPress={() => {
                                            if (gpaType === 'na') {
                                                // unselect N/A
                                                onChange('');
                                            } else {
                                                // select N/A and clear values
                                                onChange('na');
                                                setValue('gpa_g', '', { shouldValidate: true });
                                                setValue('gpa_p', '', { shouldValidate: true });
                                                setValue('gpa_a', '', { shouldValidate: true });
                                            }
                                        }}
                                    >
                                        <View
                                            style={[
                                                globalStyles.checkboxBox,
                                                gpaType === 'na' && globalStyles.checkboxChecked
                                            ]}
                                        >
                                            {gpaType === 'na' && <Text style={globalStyles.checkmark}>✓</Text>}
                                        </View>
                                        <Text style={{ marginLeft: 8 }}>N/A</Text>
                                    </TouchableOpacity>
                                </View>
                            )}
                        />

                        {/* Validation messages */}
                        {errors.gpa_g && <Text style={styles.errorText}>{errors.gpa_g.message}</Text>}
                        {errors.gpa_p && <Text style={styles.errorText}>{errors.gpa_p.message}</Text>}
                        {errors.gpa_a && <Text style={styles.errorText}>{errors.gpa_a.message}</Text>}
                    </View>


                    {/* Abnormal Serology */}
                    <View style={styles.inputContainer}>
                        <Text style={styles.label}>{t.t('Abnormal serology')}</Text>
                        <Text style={styles.sublabel}>{t.t('Can choose more than 1 item')}</Text>

                        {/* Normal all */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('abnormal_serology', 'normal_all')}>
                            <View style={[globalStyles.checkboxBox, abnormalSerology.includes('normal_all') && globalStyles.checkboxChecked]}>
                                {abnormalSerology.includes('normal_all') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Normal all serology(Anti-HIV,HBSAg,VDRL)')}</Text>
                        </TouchableOpacity>

                        {/* Positive HIV */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('abnormal_serology', 'positive_anti_hiv')}>
                            <View style={[globalStyles.checkboxBox, abnormalSerology.includes('positive_anti_hiv') && globalStyles.checkboxChecked]}>
                                {abnormalSerology.includes('positive_anti_hiv') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Positive Anti-HIV')}</Text>
                        </TouchableOpacity>

                        {/* Positive HBsAg */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('abnormal_serology', 'positive_hbsag')}>
                            <View style={[globalStyles.checkboxBox, abnormalSerology.includes('positive_hbsag') && globalStyles.checkboxChecked]}>
                                {abnormalSerology.includes('positive_hbsag') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Positive HBsAg')}</Text>
                        </TouchableOpacity>

                        {/* Positive VDRL */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('abnormal_serology', 'reactive_vdrl')}>
                            <View style={[globalStyles.checkboxBox, abnormalSerology.includes('reactive_vdrl') && globalStyles.checkboxChecked]}>
                                {abnormalSerology.includes('reactive_vdrl') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Reactive VDRL')}</Text>
                        </TouchableOpacity>

                        {/* Other */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('abnormal_serology', 'other')}>
                            <View style={[globalStyles.checkboxBox, abnormalSerology.includes('other') && globalStyles.checkboxChecked]}>
                                {abnormalSerology.includes('other') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Other')}</Text>
                        </TouchableOpacity>

                        {abnormalSerology.includes('other') && (
                            <Controller
                                control={control}
                                name="abnormal_serology_other"
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        style={styles.input}
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        placeholder={t.t('Other')}
                                    />
                                )}
                            />
                        )}
                        {errors.abnormal_serology_other && <Text style={styles.errorText}>{errors.abnormal_serology_other.message}</Text>}

                        {/* N/A */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('abnormal_serology', 'na')}>
                            <View style={[globalStyles.checkboxBox, abnormalSerology.includes('na') && globalStyles.checkboxChecked]}>
                                {abnormalSerology.includes('na') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>N/A</Text>
                        </TouchableOpacity>
                    </View>

                </View>


                {/* Complications During Pregnancy */}
                <View style={styles.inputContainer}>
                    <View style={styles.checkboxIndented}>
                        <Text style={styles.label}>{t.t('Complication during pregnancy')}</Text>
                        <Text style={styles.sublabel}>{t.t('Can choose more than 1 item')}</Text>
                    </View>
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity
                            style={styles.checkboxContainer}
                            onPress={() => toggleCheckbox('complications_pregnancy', 'no_complication')}
                        >
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('no_complication') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('no_complication') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('No complication')}</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'no_anc')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('no_anc') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('no_anc') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('No ANC')}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'overt_dm_gdm')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('overt_dm_gdm') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('overt_dm_gdm') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Overt DM / GDM')}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'chronic_ht')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('chronic_ht') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('chronic_ht') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Chronic HT / Pregnancy induced HT / Pre-eclampsia / Eclampsia')}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'maternal_thyroid')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('maternal_thyroid') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('maternal_thyroid') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Maternal thyroid disease')}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'multiple_gestation')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('multiple_gestation') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('multiple_gestation') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Multiple Gestation')}</Text>
                        </TouchableOpacity>
                    </View>
                    {complicationsPregnancy.includes('multiple_gestation') && (
                        <>
                            <Controller
                                control={control}
                                name="multiple_gestation_type"
                                render={({ field: { onChange, value } }) => (
                                    <View style={styles.subRadioGroup}>
                                        <TouchableOpacity
                                            style={styles.radioContainer}
                                            onPress={() => {
                                                onChange('twin');
                                                // Clear higher order specify when not 'higher_order'
                                                setValue('multiple_gestation_higher_order_specify', '', { shouldValidate: true });
                                            }}
                                        >
                                            <View style={globalStyles.radioButton}>
                                                {value === 'twin' && <View style={globalStyles.radioButtonSelected} />}
                                            </View>
                                            <Text>{t.t('Twin')}</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity
                                            style={styles.radioContainer}
                                            onPress={() => {
                                                onChange('higher_order');
                                            }}
                                        >
                                            <View style={globalStyles.radioButton}>
                                                {value === 'higher_order' && <View style={globalStyles.radioButtonSelected} />}
                                            </View>
                                            <Text>{t.t('Higher order')}</Text>
                                        </TouchableOpacity>

                                        {multipleGestationType === 'higher_order' && (
                                            <Controller
                                                control={control}
                                                name="multiple_gestation_higher_order_specify"
                                                render={({ field: { onChange, onBlur, value } }) => (
                                                    <TextInput
                                                        style={styles.input}
                                                        onBlur={onBlur}
                                                        onChangeText={onChange}
                                                        value={value}
                                                        placeholder={t.t('Please specify')}
                                                    />
                                                )}
                                            />
                                        )}
                                    </View>
                                )}
                            />
                            {errors.multiple_gestation_type && <Text style={styles.errorText}>{errors.multiple_gestation_type.message}</Text>}
                            {errors.multiple_gestation_higher_order_specify && <Text style={styles.errorText}>{errors.multiple_gestation_higher_order_specify.message}</Text>}
                        </>
                    )}
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'maternal_uti')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('maternal_uti') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('maternal_uti') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Maternal UTI')}</Text>
                        </TouchableOpacity>
                    </View>
                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'maternal_drug_abuse')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('maternal_drug_abuse') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('maternal_drug_abuse') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Maternal drug abuse')}</Text>
                        </TouchableOpacity>
                        {complicationsPregnancy.includes('maternal_drug_abuse') && (
                            <Controller
                                control={control}
                                name="maternal_drug_abuse_specify"
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput style={styles.input} onBlur={onBlur} onChangeText={onChange} value={value} placeholder={t.t('Please specify')} />
                                )}
                            />
                        )}
                        {errors.maternal_drug_abuse_specify && <Text style={styles.errorText}>{errors.maternal_drug_abuse_specify.message}</Text>}
                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'other')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('other') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('other') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Other')}</Text>
                        </TouchableOpacity>
                        {complicationsPregnancy.includes('other') && (
                            <Controller
                                control={control}
                                name="complications_pregnancy_other"
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput style={styles.input} onBlur={onBlur} onChangeText={onChange} value={value} placeholder={t.t('Please specify')} />
                                )}
                            />
                        )}
                        {errors.complications_pregnancy_other && <Text style={styles.errorText}>{errors.complications_pregnancy_other.message}</Text>}
                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('complications_pregnancy', 'na')}>
                            <View style={[globalStyles.checkboxBox, complicationsPregnancy.includes('na') && globalStyles.checkboxChecked]}>
                                {complicationsPregnancy.includes('na') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>N/A</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Intrapartum Complications */}
                <View style={styles.inputContainer}>
                    <View style={styles.checkboxIndented}>
                        <Text style={styles.label}>{t.t('Intrapartum complication')}</Text>
                        <Text style={styles.sublabel}>{t.t('Can choose more than 1 item')}</Text>

                        <TouchableOpacity
                            style={styles.checkboxContainer}
                            onPress={() => toggleCheckbox('intrapartum_complications', 'no_complication')}
                        >
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('no_complication') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('no_complication') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('No complication')}</Text>
                        </TouchableOpacity>

                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'abnormal_vaginal_bleeding')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('abnormal_vaginal_bleeding') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('abnormal_vaginal_bleeding') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Abnormal vaginal bleeding')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'msaf')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('msaf') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('msaf') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>MSAF</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'fetal_distress')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('fetal_distress') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('fetal_distress') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Fetal distress / abnormal NST / Decreased fetal movement')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'maternal_fever')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('maternal_fever') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('maternal_fever') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Maternal fever / Chrioamnionitis/Tripple')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'prolonged_rupture')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('prolonged_rupture') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('prolonged_rupture') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Prolonged rupture of membrane (> 18 hours)')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'prolapsed_cord')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('prolapsed_cord') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('prolapsed_cord') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Prolapsed cord')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'iugr')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('iugr') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('iugr') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>IUGR</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'oligohydramios')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('oligohydramios') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('oligohydramios') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Oligohydramios')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'polyhydramios')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('polyhydramios') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('polyhydramios') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Polyhydramios')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'other')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('other') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('other') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Other')}</Text>
                        </TouchableOpacity>
                        {intrapartumComplications.includes('other') && (
                            <Controller
                                control={control}
                                name="intrapartum_complications_other"
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput style={styles.input} onBlur={onBlur} onChangeText={onChange} value={value} placeholder={t.t('Please specify')} />
                                )}
                            />
                        )}
                        {errors.intrapartum_complications_other && <Text style={styles.errorText}>{errors.intrapartum_complications_other.message}</Text>}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('intrapartum_complications', 'na')}>
                            <View style={[globalStyles.checkboxBox, intrapartumComplications.includes('na') && globalStyles.checkboxChecked]}>
                                {intrapartumComplications.includes('na') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>N/A</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* GBS Screening */}
                <View style={styles.inputContainer}>
                    <View style={styles.checkboxIndented}>
                        <Text style={styles.label}>{t.t('GBS Screening')}</Text>
                        <Text style={styles.sublabel}>{t.t('Can choose more than 1 item')}</Text>

                        {/* Not Done */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('gbs_screening', 'not_done')}>
                            <View style={[globalStyles.checkboxBox, gbsScreening.includes('not_done') && globalStyles.checkboxChecked]}>
                                {gbsScreening.includes('not_done') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Not Done')}</Text>
                        </TouchableOpacity>

                        {/* Positive */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('gbs_screening', 'positive')}>
                            <View style={[globalStyles.checkboxBox, gbsScreening.includes('positive') && globalStyles.checkboxChecked]}>
                                {gbsScreening.includes('positive') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Positive GBS')}</Text>
                        </TouchableOpacity>

                        {/* Negative */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('gbs_screening', 'negative')}>
                            <View style={[globalStyles.checkboxBox, gbsScreening.includes('negative') && globalStyles.checkboxChecked]}>
                                {gbsScreening.includes('negative') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Negative GBS')}</Text>
                        </TouchableOpacity>

                        {/* Other */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('gbs_screening', 'other')}>
                            <View style={[globalStyles.checkboxBox, gbsScreening.includes('other') && globalStyles.checkboxChecked]}>
                                {gbsScreening.includes('other') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Other Organism')}</Text>
                        </TouchableOpacity>

                        {gbsScreening.includes('other') && (
                            <Controller
                                control={control}
                                name="gbs_screening_other"
                                render={({ field: { onChange, onBlur, value } }) => (
                                    <TextInput
                                        style={styles.input}
                                        onBlur={onBlur}
                                        onChangeText={onChange}
                                        value={value}
                                        placeholder={t.t('Specify organism')}
                                    />
                                )}
                            />
                        )}
                        {errors.gbs_screening_other && <Text style={styles.errorText}>{errors.gbs_screening_other.message}</Text>}

                        {/* N/A */}
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('gbs_screening', 'na')}>
                            <View style={[globalStyles.checkboxBox, gbsScreening.includes('na') && globalStyles.checkboxChecked]}>
                                {gbsScreening.includes('na') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>N/A</Text>
                        </TouchableOpacity>
                    </View>
                </View>


                {/* Maternal Medication */}
                <View style={styles.inputContainer}>
                    <View style={styles.checkboxIndented}>
                        <Text style={styles.label}>{t.t('Maternal medication (prior to delivery)')}</Text>
                        <Text style={styles.sublabel}>{t.t('Can choose more than 1 item')}</Text>
                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity
                            style={styles.checkboxContainer}
                            onPress={() => toggleCheckbox('maternal_medication', 'no_medication')}
                        >
                            <View style={[globalStyles.checkboxBox, maternalMedication.includes('no_medication') && globalStyles.checkboxChecked]}>
                                {maternalMedication.includes('no_medication') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('No medication')}</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('maternal_medication', 'antibiotics')}>
                            <View style={[globalStyles.checkboxBox, maternalMedication.includes('antibiotics') && globalStyles.checkboxChecked]}>
                                {maternalMedication.includes('antibiotics') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Antibiotics')}</Text>
                        </TouchableOpacity>
                    </View>

                    {maternalMedication.includes('antibiotics') && (
                        <>
                            <Controller
                                control={control}
                                name="antibiotics_timing"
                                render={({ field: { onChange, value } }) => (
                                    <View style={styles.subRadioGroup}>
                                        <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('≥ 4 hr prior to delivery')}>
                                            <View style={globalStyles.radioButton}>
                                                {value === '≥ 4 hr prior to delivery' && <View style={globalStyles.radioButtonSelected} />}
                                            </View>
                                            <Text>{t.t('≥ 4 hr prior to delivery')}</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('< 4 hr prior to delivery')}>
                                            <View style={globalStyles.radioButton}>
                                                {value === '< 4 hr prior to delivery' && <View style={globalStyles.radioButtonSelected} />}
                                            </View>
                                            <Text>{t.t('< 4 hr prior to delivery')}</Text>
                                        </TouchableOpacity>
                                    </View>
                                )}
                            />
                            {errors.antibiotics_timing && <Text style={styles.errorText}>{errors.antibiotics_timing.message}</Text>}
                        </>
                    )}

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('maternal_medication', 'dexamethasone_prenatal_steroid')}>
                            <View style={[globalStyles.checkboxBox, maternalMedication.includes('dexamethasone_prenatal_steroid') && globalStyles.checkboxChecked]}>
                                {maternalMedication.includes('dexamethasone_prenatal_steroid') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('prenatal steroid / Dexamethasone')}</Text>
                        </TouchableOpacity>
                    </View>

                    {maternalMedication.includes('dexamethasone_prenatal_steroid') && (
                        <>
                            <Controller
                                control={control}
                                name="prenatal_steroid_course"
                                render={({ field: { onChange, value } }) => (
                                    <View style={styles.subRadioGroup}>
                                        <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('partial course')}>
                                            <View style={globalStyles.radioButton}>
                                                {value === 'partial course' && <View style={globalStyles.radioButtonSelected} />}
                                            </View>
                                            <Text>{t.t('Partial course')}</Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity style={styles.radioContainer} onPress={() => onChange('complete course')}>
                                            <View style={globalStyles.radioButton}>
                                                {value === 'complete course' && <View style={globalStyles.radioButtonSelected} />}
                                            </View>
                                            <Text>{t.t('Complete course')}</Text>
                                        </TouchableOpacity>
                                    </View>
                                )}
                            />
                            {errors.prenatal_steroid_course && <Text style={styles.errorText}>{errors.prenatal_steroid_course.message}</Text>}
                        </>
                    )}

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('maternal_medication', 'mgso4')}>
                            <View style={[globalStyles.checkboxBox, maternalMedication.includes('mgso4') && globalStyles.checkboxChecked]}>
                                {maternalMedication.includes('mgso4') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>MgSO4</Text>
                        </TouchableOpacity>
                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('maternal_medication', 'other')}>
                            <View style={[globalStyles.checkboxBox, maternalMedication.includes('other') && globalStyles.checkboxChecked]}>
                                {maternalMedication.includes('other') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>{t.t('Other')}</Text>
                        </TouchableOpacity>
                        {maternalMedication.includes('other') && (
                            <View style={{ marginTop: 8 }}>
                                {otherMedicationFields.map((field, index) => (
                                    <View key={field.id} style={{ marginBottom: 10 }}>
                                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                                            <TextInput
                                                style={[styles.input, { flex: 1, marginRight: 8 }]}
                                                onChangeText={(text) => updateOtherMedicationValue(field.id, text)}
                                                value={field.value}
                                                placeholder={t.t('Please specify')}
                                            />
                                            <TouchableOpacity
                                                style={[styles.quantityButton, { marginLeft: 8 ,backgroundColor:colors.button.red}]}
                                                onPress={() => removeOtherMedicationField(field.id)}
                                            >
                                                <Text style={styles.quantityButtonText}>-</Text>
                                            </TouchableOpacity>
                                        </View>
                                        {index === otherMedicationFields.length - 1 && (
                                            <TouchableOpacity
                                                style={[styles.quantityButton, { alignSelf: 'flex-end', marginTop: 8,backgroundColor:colors.button.primary }]}
                                                onPress={addOtherMedicationField}
                                            >
                                                <Text style={styles.quantityButtonText}>+</Text>
                                            </TouchableOpacity>
                                        )}
                                    </View>
                                ))}
                                {errors.maternal_medication_other && (
                                    <Text style={styles.errorText}>{errors.maternal_medication_other.message}</Text>
                                )}
                            </View>
                        )}

                    </View>

                    <View style={styles.checkboxIndented}>
                        <TouchableOpacity style={styles.checkboxContainer} onPress={() => toggleCheckbox('maternal_medication', 'na')}>
                            <View style={[globalStyles.checkboxBox, maternalMedication.includes('na') && globalStyles.checkboxChecked]}>
                                {maternalMedication.includes('na') && <Text style={globalStyles.checkmark}>✓</Text>}
                            </View>
                            <Text style={styles.checkboxLabel}>N/A</Text>
                        </TouchableOpacity>

                        {errors.maternal_medication && <Text style={styles.errorText}>{errors.maternal_medication.message}</Text>}
                        {errors.antibiotics_timing && <Text style={styles.errorText}>{errors.antibiotics_timing.message}</Text>}
                        {errors.dexamethasone_course && <Text style={styles.errorText}>{errors.dexamethasone_course.message}</Text>}
                        {/* {errors.maternal_medication_other && <Text style={styles.errorText}>{errors.maternal_medication_other.message}</Text>} */}
                    </View>
                </View>

                {/* <TouchableOpacity style={styles.button} onPress={handleSubmit(onSubmit)}>
                    <Text style={styles.buttonText}>{t.t('Save')}</Text>
                </TouchableOpacity> */}

            </KeyboardAwareScrollView>
            <CommonFooter
                onSubmit={handleSubmit(onSubmit, onError)}
                buttonText={t.t('Save')}
                loading={loading}
            />
        </ScreenWrapper>
    );
};

const styles = StyleSheet.create({
    scrollContainer: {
        padding: 20,
    },
    subHeadingContainer: {
        borderBottomWidth: 1,
        borderBottomColor: colors.border.gray,
        marginBottom: 20,
    },
    subHeader: {
        fontSize: 18,
        fontWeight: 'bold',
        paddingBottom: 3,
    },
    inputContainer: {
        marginBottom: 20,
    },
    rowContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },

    gpaField: {
        flex: 1,
        marginRight: 10,
    },
    label: {
        fontSize: 16,
        // marginBottom: 10,
        fontWeight: 'bold',
    },
    sublabel: {
        fontSize: 12,
        color: '#666',
        marginBottom: 10,
    },
    inputtext: {
        fontSize: 15,
        marginBottom: 10,
    },
    input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 10,
        marginVertical: 5,
        backgroundColor: '#fff',
    },
    quantityButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#ccc',
    },
    quantityButtonText: {
        fontSize: 23,
        color: '#fff',
        lineHeight: 24,
    },
    radioContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 10,
    },
    radioButton: {
        height: 24,
        width: 24,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#7FC5C6',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
    },
    radioButtonSelected: {
        height: 12,
        width: 12,
        borderRadius: 6,
        backgroundColor: '#7FC5C6',
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 12,

    },
    checkboxBox: {
        width: 22,
        height: 22,
        borderWidth: 2,
        borderColor: '#7FC5C6',
        marginRight: 10,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5
    },
    checkboxChecked: {
        backgroundColor: '#7FC5C6',
    },
    // checkmark: {
    //     color: '#fff',
    //     fontSize: 16,
    //     fontWeight: 'bold',
    // },
    checkmark: {
        color: '#fff',
        fontSize: 14,              // tweak 12–16 to taste
        lineHeight: 20,            // match checkboxBox height
        textAlign: 'center',
        textAlignVertical: 'center', // Android
        includeFontPadding: false,   // Android
    },

    checkboxLabel: {
        fontSize: 16,
        color: '#000',
    },
    button: {
        backgroundColor: colors.button.primary,
        padding: 15,
        borderRadius: 25,
        alignItems: 'center',
        marginTop: 20,
    },
    buttonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginBottom: 5,
    },
    subRadioGroup: {
        marginTop: 10,
        backgroundColor: "#F5F5F5",
        paddingLeft: 26,
        paddingVertical: 4,
        paddingRight: 10
    },
    checkboxIndented: {
        paddingLeft: 16, // adjust the left padding as you like
        paddingRight: 10

    },


});

export default Section2Form;
