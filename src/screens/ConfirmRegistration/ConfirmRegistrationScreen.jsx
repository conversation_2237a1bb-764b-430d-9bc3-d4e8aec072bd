import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import CompleteIcon from '.././../assets/img/complete.svg';
import { useRouter } from 'expo-router';
import { useSelector, useDispatch } from 'react-redux';
import ROUTES from '../../config/routes';
import LanguageContext from '../../context/LanguageContext';
import { Ionicons } from '@expo/vector-icons';
import colors from '../../theme/colors';
import { resetCriteria } from '../../features/register/registerSlice';

const ConfirmRegistrationScreen = () => {
    const router = useRouter();
    const dispatch = useDispatch();
    const { TNR, formData } = useSelector(state => state.register);
    const { t } = useContext(LanguageContext);
    
    const proceedToNextSection = () => {
        router.replace(ROUTES.PRIVATE.PATIENT_DISPLAY_SCREEN);
    };

    const goBackToPatientList = () => {
        dispatch(resetCriteria());
        router.replace(ROUTES.PRIVATE.PATIENT_LIST);
    };

    return (
        <SafeAreaView style={styles.container}>
            {/* BODY SECTION */}
            <ScrollView
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
                style={styles.body}
            >
                {/* Emoji or icon */}
                <View style={styles.emojiContainer}>
                    <CompleteIcon width={107} height={107} />
                </View>

                {/* Title & description */}
                <View style={styles.textContainer}>
                    <Text style={styles.successTitle}>{t.t('Registration completed successfully')}</Text>
                    <Text style={styles.description}>
                        {t.t('System has generated TNR No for')}{'\n'}"{formData?.infantName || 'N/A'}" {t.t('successfully')}
                    </Text>
                    <Text style={styles.tnr}>TNR No: {TNR || 'xxxx-aaa-bbbb-ccc'}</Text>
                </View>

                {/* Patient Information */}
                <View style={styles.infoSection}>
                    <Text style={styles.infoTitle}>{t.t('Infant (patient) information')}</Text>
                    <View style={styles.underline} />

                    <Text style={styles.infoText}>{formData?.infantName || 'N/A'}</Text>
                    <Text style={styles.infoText}>HN: {formData?.hn || '7327934'}</Text>
                    <Text style={styles.infoText}>{t.t('Mother')}: {formData?.motherName || 'N/A'}</Text>
                </View>

            </ScrollView>

            {/* FOOTER SECTION */}
            <View style={styles.footer}>
                <TouchableOpacity style={styles.secondaryButton} onPress={goBackToPatientList}>
                    <Text style={styles.secondaryButtonText}>{t.t('Go to Patients List page')}</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.primaryButton} onPress={proceedToNextSection}>
                    <View style={styles.buttonContent}>
                        <Text style={styles.primaryButtonText}>{t.t('Start recording next section data')}</Text>
                        <Ionicons name="chevron-forward" size={16} color="#4BA3A2" />
                    </View>
                </TouchableOpacity>
            </View>
        </SafeAreaView>
    );
};




export default ConfirmRegistrationScreen;


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor:colors.background.green,
    },
    body: {
        flex: 1,
        paddingHorizontal: 20,
    },
    scrollContent: {
        flexGrow: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    footer: {
        paddingHorizontal: 20,
        paddingVertical: 15,
        backgroundColor: colors.footer.green,
    },
    emojiContainer: {
        marginBottom: 20,
    },
    emoji: {
        width: 100,
        height: 100,
    },
    textContainer: {
        alignItems: 'center',
        marginBottom: 20,
    },
    successTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
        marginBottom: 10,
        textAlign: 'center',
    },
    description: {
        textAlign: 'center',
        color: 'black',
        fontSize: 16,
        marginBottom: 10,
    },
    tnr: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#fff',
    },
        infoSection: {
        width: '100%',
        marginBottom: 30,
    },
    infoTitle: {
        fontWeight: 'bold',
        fontSize: 17,
        color: '#fff',
        marginBottom: 6,
    },
    underline: {
        height: 1,
        backgroundColor: '#fff',
        width: '100%',
        marginBottom: 10,
    },
    infoText: {
        fontSize: 17,
        color: '#fff',
        marginBottom: 4,
    },
    secondaryButton: {
        borderWidth: 1,
        borderColor: '#fff',
        borderRadius: 30,
        paddingVertical: 12,
        paddingHorizontal: 20,
        marginBottom: 15,
    },
    secondaryButtonText: {
        color: '#fff',
        fontSize: 14,
        textAlign: 'center',
    },
    primaryButton: {
        backgroundColor: '#fff',
        borderRadius: 30,
        paddingVertical: 12,
        paddingHorizontal: 20,
    },
    buttonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    primaryButtonText: {
        color: '#4BA3A2',
        fontSize: 14,
        fontWeight: 'bold',
        marginRight: 8,
    },
});
