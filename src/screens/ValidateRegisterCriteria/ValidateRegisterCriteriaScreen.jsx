import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import colors from '../../theme/colors';
import { SafeAreaView } from 'react-native-safe-area-context';
import { getValidationStatus } from '../../features/register/registerCriteriaValidate'
import { useDispatch, useSelector } from 'react-redux';
import LanguageContext from '../../context/LanguageContext';
import ROUTES from '../../config/routes';

const ValidationRegisterCriteriaScreen = () => {
    const registerCriteria = useSelector((state) => state.register);
    const { pna, symptoms } = registerCriteria;
    const router = useRouter();
    const { t } = useContext(LanguageContext);

    const { isValid, screenConfig } = getValidationStatus(pna, symptoms, t);
    const handleButtonPress = () => {
        if (isValid) {
            router.replace(ROUTES.PRIVATE.REGISTRATION);
        } else {
            router.replace(ROUTES.PRIVATE.PATIENT_LIST);

        }
    };

    // Build criteria list for display (no Admit Ward in new flow)
    const pnaNum = Number(pna);
    const pnaLabel = Number.isFinite(pnaNum)
        ? (pnaNum > 28 ? t.t('> 28 days PNA') : t.t('<= 28 days PNA'))
        : 'N/A';
    const criteriaList = [
        {
            label: t.t('PNA (Postnatal Age)'),
            value: pnaLabel,
            valid: Number.isFinite(pnaNum) && pnaNum <= 28,
        },
        {
            label: t.t('Symptoms'),
            value: symptoms,
            valid: Array.isArray(symptoms) && symptoms.length > 0,
        },
    ];

    return (
        <SafeAreaView style={[styles.safeArea, { backgroundColor: screenConfig.backgroundColor }]}>
            <View style={[styles.container, { backgroundColor: screenConfig.backgroundColor }]}>
                {/* Header */}
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
                        <Ionicons name="chevron-back" size={20} color="#fff" style={{ marginRight: 4 }} />
                        <Text style={styles.backText}>{t.t('Back')}</Text>
                    </TouchableOpacity>
                </View>

                {/* Main Content */}
                <View style={styles.mainContent}>
                    <View style={styles.topContent}>
                        <View style={styles.icon}>
                            {screenConfig.svg}
                        </View>
                        <Text style={styles.title}>{screenConfig.title}</Text>
                        <Text style={styles.description}>{screenConfig.description}</Text>
                    </View>

                    <ScrollView style={styles.optionsList}>
                        {criteriaList.map((item, idx) => {
                            const isEmpty = (Array.isArray(item.value) && item.value.length === 0) || (!Array.isArray(item.value) && !item.value);

                            return (
                                <View key={idx} style={styles.optionRow}>
                                    <Text style={styles.optionLabel}>{item.label}</Text>

                                    {/* Values */}
                                    <View style={{ flex: 1 }}>
                                        {isEmpty ? (
                                            <View style={styles.valueRow}>
                                                <Text style={[styles.optionValue]}>N/A</Text>
                                            </View>
                                        ) : Array.isArray(item.value) ? (
                                            item.value.map((val, i) => (
                                                <View key={i} style={styles.valueRow}>
                                                    <Ionicons
                                                        name={item.valid ? "checkmark-circle-outline" : "close-circle-outline"}
                                                        size={20}
                                                        style={styles.checkIcon}
                                                    />
                                                    <Text
                                                        style={[
                                                            styles.optionValue,
                                                            // item.selected ? styles.selected : styles.notSelected,
                                                        ]}
                                                    >
                                                        {val}
                                                    </Text>
                                                </View>
                                            ))
                                        ) : (
                                            <View style={styles.valueRow}>
                                                <Ionicons
                                                    name={item.valid ? "checkmark-circle-outline" : "close-circle-outline"}
                                                    size={20}
                                                    color={item.valid ? "#4CAF50" : "#F44336"}
                                                    style={styles.checkIcon}
                                                />
                                                <Text
                                                    style={[
                                                        styles.optionValue,
                                                        // item.selected ? styles.selected : styles.notSelected,
                                                    ]}
                                                >
                                                    {item.value}
                                                </Text>
                                            </View>
                                        )}
                                    </View>
                                </View>
                            );
                        })}

                    </ScrollView>

                </View>

                {/* Footer */}
                <View style={styles.footer}>
                    <TouchableOpacity style={styles.button} onPress={handleButtonPress}>
                        <Text style={styles.buttonText}>{screenConfig.buttonText}</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );

};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
    },

    container: {
        flex: 1,
        paddingHorizontal: 20
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 5
    },
    backButton: {
        paddingRight: 10,
        flexDirection: 'row',
    },
    backText: {
        color: '#fff',
        fontSize: 16
    },
    mainContent: {
        flex: 1,
        justifyContent: "center"
    },
    topContent: {
        alignItems: 'center',
        marginBottom: 20
    },
    checkIcon: {
        color: "#fff"
    },
    icon: {
        alignItems: 'center',
        justifyContent: 'center',
    },

    title: {
        marginTop: 10,
        fontWeight: 'bold',
        fontSize: 20,
        color: '#fff'
    },
    description: {
        marginTop: 6,
        fontSize: 15,
        color: colors.text.desaturatedCyan,
        textAlign: 'center'
    },
    optionsList: { flexGrow: 0 },
    optionRow: {
        paddingBottom: 15
    },
    optionBlock: {
        marginBottom: 16,
    },
    valueRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
    },

    optionLabel: {
        fontWeight: 'bold',
        color: '#fff',
        fontSize: 17,
        marginBottom: 4,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.gray
    },

    optionValue: {
        fontSize: 17,
        color: '#fff',
    },

    selected: {
        color: '#fff'
    },
    notSelected: {
        color: '#ffb0b0'
    },
    footer: {
        paddingVertical: 20
    },
    button: {
        backgroundColor: '#fff',
        borderRadius: 25,
        paddingVertical: 12,
        alignItems: 'center',
    },
    buttonText: {
        color: '#555',
        fontWeight: 'bold',
        fontSize: 16
    },
});

export default ValidationRegisterCriteriaScreen;
