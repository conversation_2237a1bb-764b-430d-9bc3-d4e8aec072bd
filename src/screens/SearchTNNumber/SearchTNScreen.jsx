import React, { useState, useContext } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import colors from '../../theme/colors';
import { responsive } from '../../utils/responsive';
import LanguageContext from '../../context/LanguageContext';
import { useDispatch, useSelector } from 'react-redux';
import { fetchPatientByTnrOrName } from '../../features/patients/patientsSlice';
import ROUTES from '../../config/routes';

const SearchTNScreen = () => {
    const router = useRouter();
    const [searchValue, setSearchValue] = useState('');
    const { t } = useContext(LanguageContext);
    const dispatch = useDispatch();
    const { singlePatient, loading } = useSelector((state) => state.patients);
    const handleBack = () => {
        router.replace('/privateRoutes/home');
    };

   const handleSearch = async () => {
    if (searchValue.trim()) {
      await dispatch(fetchPatientByTnrOrName(searchValue.trim()));

      // Navigate if patient found
      if (singlePatient) {
        router.push(ROUTES.PRIVATE.PATIENT_DISPLAY_SCREEN);
      }
    }
  };

    return (
        <SafeAreaView style={{ flex: 1, backgroundColor: colors.header.background }} edges={["top"]}>
            <View style={styles.container}>
                {/* Header Section */}
                <View style={styles.headerSection}>
                    <TouchableOpacity style={styles.backRow} onPress={handleBack}>
                        <Ionicons name="chevron-back" size={24} color="#7FC5C6" />
                        <Text style={styles.backText}>{t.t('Back')}</Text>
                    </TouchableOpacity>
                    <Text style={styles.title}>{t.t('Search TN Number')}</Text>
                </View>
                {/* Main Content Section */}
                <View style={styles.mainSection}>
                    <Text style={styles.description}>
                        {t.t("Search for the TN number associated with the patient by using the TN number or the patient's name to search.")}
                    </Text>
                    <TextInput
                        style={styles.input}
                        placeholder={t.t('TN number / Patient name')}
                        placeholderTextColor={colors.text.placeholder}
                        value={searchValue}
                        onChangeText={setSearchValue}
                    />
                    {/* <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
                        <Text style={styles.searchButtonText}>{t.t('Search')}</Text>
                    </TouchableOpacity> */}
                    <TouchableOpacity style={styles.searchButton} onPress={handleSearch} disabled={loading}>
                        {loading ? (
                        <ActivityIndicator color="#fff" />
                        ) : (
                        <Text style={styles.searchButtonText}>{t.t('Search')}</Text>
                        )}
                    </TouchableOpacity>

                    {singlePatient === null && !loading && (
                        <Text style={{ marginTop: 20, color: 'red' }}>{t.t('No patient found')}</Text>
                    )}
                </View>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.background.white,
    },
    headerSection: {
        paddingHorizontal: responsive(16),
        backgroundColor: colors.header.background,
        justifyContent: 'flex-start',
        borderBottomWidth: 1,
        borderBottomColor: colors.border.main
    },
    backRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    backText: {
        color: colors.text.navBarGreen,
        fontSize: 16,
        marginLeft: 2,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#222',
        marginTop: 8,
        paddingBottom: 8
    },
    mainSection: {
        flex: 2,
        paddingHorizontal: responsive(16),
        paddingTop: responsive(16),
        backgroundColor: colors.background.white,
        alignItems: 'center',
    },
    description: {
        fontSize: 15,
        color: '#222',
        marginTop: 16,
        marginBottom: 16,
    },
    input: {
        width: '100%',
        height: 44,
        borderWidth: 1,
        borderColor: '#E0E0E0',
        borderRadius: 8,
        paddingHorizontal: responsive(12),
        fontSize: 16,
        marginBottom: 24,
        backgroundColor: '#fff',
    },
    searchButton: {
        width: '100%',
        height: 44,
        backgroundColor: '#7FC5C6',
        borderRadius: 24,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 8,
    },
    searchButtonText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default SearchTNScreen; 