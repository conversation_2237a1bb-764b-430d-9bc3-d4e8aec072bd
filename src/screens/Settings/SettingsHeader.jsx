import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { responsive } from '../../utils/responsive';
import colors from '../../theme/colors';
import LanguageContext from '../../context/LanguageContext';
import BackIcon from '../../assets/img/backgreen.svg'
import { useRouter } from 'expo-router';
import ROUTES from '../../config/routes'

const SettingsHeader = () => {
    const { t } = useContext(LanguageContext);
    const router=useRouter()
const handleBack=()=>{
router.replace(ROUTES.PRIVATE.HOME)
}
    return (
        <>
            <View style={styles.topSpacer} />

            <View style={styles.header}>
                <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                    <BackIcon width={20} height={20} />
                    <Text style={styles.backText}>{t.t('Back')}</Text>
                </TouchableOpacity>

                <Text style={styles.headerTitle}>{t.t('Settings')}</Text>
            </View>
        </>

    );
};

export default SettingsHeader;

const styles = StyleSheet.create({
    topSpacer: {
    paddingTop: responsive(24),
    backgroundColor: colors.header.background, 
},

    header: {
        flexDirection: 'column',
        paddingHorizontal: responsive(5),
        paddingTop: responsive(20),
        paddingBottom: responsive(10),
        backgroundColor: colors.header.background,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.main,
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
    },
    backText: {
        fontSize: 16,
        color: colors.text.navBarGreen,
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: '600',
        color: '#000',
        paddingLeft: 10,
        marginTop:5
    },
    avatar: {
        width: 40,
        height: 40,
        backgroundColor: 'red',
        borderRadius: 20,
    },
});
