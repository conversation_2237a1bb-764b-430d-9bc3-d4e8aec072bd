import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import SettingsHeader from './SettingsHeader';
import LanguageContext from '../../context/LanguageContext';
import NextIcon from '../../assets/img/next2.svg'
import NotificationIcon from '../../assets/img/setting/notification.svg';
import PasscodeIcon from '../../assets/img/setting/passcode.svg';
import PasswordIcon from '../../assets/img/setting/password.svg';


const settingsOptions = [
  { id: '1', label: 'Notification', Icon: NotificationIcon },
  { id: '2', label: 'Change Passcode', Icon: PasscodeIcon },
  { id: '3', label: 'Change User Account Password', Icon: PasswordIcon },
];


const SettingsScreen = () => {
  const { t } = useContext(LanguageContext)
  return (
    <View style={styles.container}>
      {/* Header */}
      <SettingsHeader />

      {/* Main Content */}
      <View style={styles.mainContent}>
        {settingsOptions.map((item) => (
          <TouchableOpacity key={item.id} style={styles.optionRow}>
            {/* Left-side icon */}
            <item.Icon width={25} height={25} style={styles.optionIcon} />

            {/* Content wrapper: Label + Right arrow */}
            <View style={styles.optionContent}>
              <Text style={styles.optionLabel}>{t.t(item.label)}</Text>
              <NextIcon width={15} height={15} />
            </View>
          </TouchableOpacity>
        ))}
      </View>




      {/* Footer (Optional) */}
      <View style={styles.footer}>
        {/* Add footer content here if needed */}
      </View>
    </View>
  );
};

export default SettingsScreen;


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },

  // Header
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 12,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
  },
  optionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 14,
    paddingRight:4
  },

  backText: {
    color: '#25b2bd',
    fontSize: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
  },
  avatar: {
    width: 40,
    height: 40,
    backgroundColor: 'red',
    borderRadius: 20,
  },

  // Main
  mainContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
  },
  optionIcon: {
    marginRight: 10,
  },
  optionLabel: {
    flex: 1,
    fontSize: 16,
    color: '#000',
  },
  chevronIcon: {
    marginLeft: 10,
  },

  // Footer (Optional)
  footer: {
    padding: 10,
  },
});
