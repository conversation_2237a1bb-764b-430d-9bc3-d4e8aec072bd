import React, { useEffect } from 'react';
import { View, Text, FlatList, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'expo-router';
import { fetchReferredPatients,setCameFromReferList } from '../../features/patients/patientsSlice';
import colors from '../../theme/colors';
import LanguageContext from '../../context/LanguageContext';
import ScreenWrapper from '../../components/ScreenWrapper';
import CommonHeader from '../../components/CommonHeader';
import ListItem from '../../components/ListItem';
import ROUTES from '../../config/routes';

const ReferListScreen = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { t } = React.useContext(LanguageContext);

  const referredList = useSelector(state => state.patients.referredPatients);
  const hospital = useSelector(state => state.auth?.user?.hospital);

  useEffect(() => {
    if (hospital) {
      dispatch(fetchReferredPatients());
    }
  }, [dispatch, hospital]);

  const handlePatientPress = (patient) => {
        dispatch(setCameFromReferList(true));
        router.push(ROUTES.PRIVATE.REGISTRATION);
    };

  const renderItem = ({ item }) => (
    <ListItem
      title={`${item.fullname} ${item.condition ? item.condition : ''}`}
      subtitle={`TN#: ${item.TNR}`}
      status={'Waiting for acceptance'}
      onPress={() => handlePatientPress(item)}
      // onPress={() => console.log('Navigate to patient details for:', item.TNR)}
    />
  );

  return (
    <ScreenWrapper topColor={colors.header.background}>
      <CommonHeader title={t.t('Referred Patients List')} onBackPress={() => router.back()} />
      <FlatList
        data={referredList}
        renderItem={renderItem}
        keyExtractor={item => item.TNR}
        ListEmptyComponent={<Text style={styles.empty}>{t.t('No referred patients')}</Text>}
      />
      <View style={styles.footer}>
        <Text style={styles.footerText}>{`${t.t('Number of referred patients')} ${referredList ? referredList.length: 0}`}</Text>
      </View>
    </ScreenWrapper>
  );
};

export default ReferListScreen;

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     padding: 20,
//     backgroundColor: '#fff',
//   },
//   title: {
//     fontSize: 22,
//     fontWeight: 'bold',
//     marginBottom: 16,
//   },
//   itemContainer: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     paddingVertical: 14,
//     borderBottomWidth: 1,
//     borderColor: '#ddd',
//   },
//   infoSection: {
//     flex: 1,
//   },
//   name: {
//     fontSize: 16,
//     fontWeight: 'bold',
//   },
//   tn: {
//     fontSize: 14,
//     color: '#666',
//   },
//   button: {
//     backgroundColor: colors.button.primary,
//     paddingHorizontal: 14,
//     paddingVertical: 6,
//     borderRadius: 12,
//   },
//   buttonText: {
//     color: '#fff',
//     fontSize: 14,
//   },
//   count: {
//     marginTop: 20,
//     textAlign: 'center',
//     fontSize: 16,
//   },
//   empty: {
//     textAlign: 'center',
//     marginTop: 30,
//     color: '#888',
//   },
// });

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  footer: {
    padding: 15,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#8A8A8E',
  },
});