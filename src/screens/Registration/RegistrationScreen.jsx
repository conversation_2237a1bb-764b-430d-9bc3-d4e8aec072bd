import React from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    ScrollView,
    StyleSheet,
    Switch
} from 'react-native';
import { responsive } from '../../utils/responsive';
import colors from '../../theme/colors';
import ScreenWrapper from '../../components/ScreenWrapper';
import CommonHeader from '../../components/CommonHeader';
import RegistrationForm from './RegistrationForm';
import { useContext } from 'react';
import LanguageContext from '../../context/LanguageContext';
import ROUTES from '../../config/routes';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useDispatch, useSelector } from 'react-redux';
import { setCameFromReferList } from '../../features/patients/patientsSlice';

export default function RegistrationScreen() {
    const router = useRouter();
    const params = useLocalSearchParams();
     const dispatch = useDispatch();
    const { TNR: paramTNR, hospital: paramHospital } = params;
    const { t } = useContext(LanguageContext);
      const cameFromReferList = useSelector(state => state.patients.cameFromReferList);

    const handleBack=()=>{
        if(paramTNR && paramHospital){
        router.replace(ROUTES.PRIVATE.PATIENT_DISPLAY_SCREEN)
        }else{
            router.replace(ROUTES.PRIVATE.REGISTER_CRITERIA)
        }
        if (cameFromReferList) {
            // dispatch(setCameFromReferList(false)); // Reset flag
            // router.replace(ROUTES.PRIVATE.REFER_LIST); // Navigate back to Refer List
            router.back();
        }
    }
    return (
        <ScreenWrapper topColor={colors.header.background}>
            <CommonHeader title={t.t('Registration')} onBackPress={handleBack} />
            <View style={styles.container}>
                {/* Scrollable Content */}
                <RegistrationForm paramTNR={paramTNR} paramHospital={paramHospital} />
            </View>
        </ScreenWrapper>
    );
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff'
    },


});
