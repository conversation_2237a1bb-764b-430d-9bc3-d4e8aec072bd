import React from 'react';
import { StyleSheet } from 'react-native';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import colors from '../theme/colors';

const ScreenWrapper = ({ children, topColor, bottomColor }) => {
    return (
        <View style={styles.container}>
            <SafeAreaView
                edges={['top']}
                style={{ flex: 0, backgroundColor: topColor || colors.background.main }}
            />
            <View style={styles.contentContainer}>
                {children}
            </View>
            <SafeAreaView
                edges={['bottom']}
                style={{ flex: 0, backgroundColor: bottomColor || colors.background.white }}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.background.white, // Set a default background for the whole screen
    },
    contentContainer: {
        flex: 1,
    },
});

export default ScreenWrapper;
