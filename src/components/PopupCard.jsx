import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Dialog, Portal } from 'react-native-paper';
import colors from '../theme/colors';
import fonts from '../theme/fonts';

const PopupCard = ({
    visible = true,
    title,
    description,
    backgroundColor = '#fff',
    svg,
    buttons = [],
    containerStyle,
    titleStyle,
    descriptionStyle,
    onDismiss, // Add onDismiss to handle closing the dialog
}) => {
    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onDismiss} style={[styles.card, { backgroundColor }, containerStyle]}>
                <Dialog.Content style={styles.stickerContainer}>
                    {svg && <View style={styles.icon}>{svg}</View>}
                    {title && <Text style={[styles.title, titleStyle]}>{title}</Text>}
                    {description && <Text style={[styles.description, descriptionStyle]}>{description}</Text>}
                </Dialog.Content>
                <View style={styles.buttonRow}>
                    {buttons.map((btn, idx) => (
                        <React.Fragment key={btn.label + idx}>
                            <TouchableOpacity style={styles.button} onPress={btn.onPress}>
                                <Text style={[styles.buttonText, { color: btn.color }]}>{btn.label}</Text>
                            </TouchableOpacity>
                            {idx < buttons.length - 1 && <View style={styles.divider} />}
                        </React.Fragment>
                    ))}
                </View>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    card: {
        width: 260,
        borderRadius: 16,
        alignItems: 'center',
        paddingVertical: 0,
        paddingHorizontal: 0,
        overflow: 'hidden',
        alignSelf: 'center',
    },
    stickerContainer: {
        alignItems: 'center',
        paddingTop: 24,
        paddingBottom: 30,
        paddingHorizontal: 12,
        width: '100%',
    },
    icon: {
        marginBottom: 8,
    },
    title: {
        color: '#fff',
        fontSize: fonts.card.titleSize,
        fontWeight: 'bold',
        marginBottom: 6
    },
    description: {
        textAlign: 'center',
        color: colors.text.cardText,
        fontSize: fonts.card.messageSize,
        marginBottom: 0
    },
    buttonRow: {
        paddingVertical: 0,
        paddingHorizontal: 0,
        flexDirection: 'row',
        width: '100%',
        borderTopWidth: 1,
        borderTopColor: '#fff',
        backgroundColor: '#fff',
        height: 48,
        justifyContent: 'space-evenly',
    },
    button: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        borderRadius: 0,
    },
    divider: {
        width: 1,
        backgroundColor: '#6d4c1c',
        height: '100%',
    },
    buttonText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default PopupCard;
 