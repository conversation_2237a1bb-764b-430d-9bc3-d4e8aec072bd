import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import colors from '../theme/colors';
import { ActivityIndicator } from 'react-native-paper';

const CommonFooter = ({ onSubmit, buttonText, loading }) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={[styles.button, loading && { opacity: 0.6 }]}
        onPress={!loading ? onSubmit : null} // disable press if loading
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>{buttonText}</Text>
        )}
      </TouchableOpacity>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingTop:8,
    backgroundColor: colors.white,
  },
  button: {
    backgroundColor: colors.button.primary,
    padding: 15,
    borderRadius: 25,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default CommonFooter;
