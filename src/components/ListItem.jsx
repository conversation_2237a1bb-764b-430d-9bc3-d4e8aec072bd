import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Next2Svg from '../assets/img/next2.svg';
import { responsive } from '../utils/responsive';
import LanguageContext from '../context/LanguageContext';
import colors from '../theme/colors';
import {getStatusProps} from '../utils/statusColor'
  
  const ListItem = React.memo(({ title, subtitle, badge,status, refer_status, onPress }) => {
    const { t } = useContext(LanguageContext);
    const statusProps = getStatusProps(status,refer_status);
  
    return (
      <TouchableOpacity style={styles.row} onPress={onPress}>
        <View style={styles.info}>
          <Text style={styles.title}>{title}</Text>
          {subtitle ? <Text style={styles.subtitle}>{subtitle}</Text> : null}
        </View>
        <View style={styles.right}>
          {statusProps.text && (
            <View style={[styles.statusBadge, { backgroundColor: statusProps.color }]}>
              <Text style={styles.statusBadgeText}>{t.t(statusProps.text)}</Text>
            </View>
          )}
          {badge !== undefined && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{badge}</Text>
            </View>
          )}
          <Next2Svg width={14} height={14} style={styles.chevronIcon} />
        </View>
      </TouchableOpacity>
    );
  });
  
const styles = StyleSheet.create({
    row: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingLeft: responsive(18),
        paddingRight: responsive(18),
        paddingTop: responsive(10),
        paddingBottom: responsive(10),
        borderBottomColor:colors.border.gray,
        borderBottomWidth:0.5
    },
    info: {
        flex: 1,
    },
    title: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    subtitle: {
        fontSize: 14,
        color: '#888',
    },
    right: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusBadge: {
        backgroundColor: '#007AFF',
        borderRadius: 8,
        paddingHorizontal: responsive(8),
        paddingVertical: responsive(4),
        marginRight: 8,
    },
    statusBadgeText: {
        fontSize: 12,
        color: '#fff',
    },
    badge: {
        width: 24,
        height: 24,
        backgroundColor: 'red',
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 4,
    },
    badgeText: {
        color: 'white',
        fontSize: 15,
    },
    chevronIcon: {
        marginLeft: 4,
    },
});

export default ListItem;