import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import Popover from 'react-native-popover-view';

const SortPopupModal = ({
    visible,
    onClose,
    onApply,
    onSelectOption,
    selectedValues,
    sections = [],
    anchorRef,
    cancelText = 'ยกเลิก',
    applyText = 'นำไปใช้',
}) => {
    return (
        <Popover
            isVisible={visible}
            onRequestClose={onClose}
            from={anchorRef}
            popoverStyle={styles.container}
            arrowStyle={styles.arrow}
            animationConfig={{ duration: 200 }}
        >
            <View style={styles.innerContainer}>
                {sections.map((section) => (
                    <View key={section.key} style={styles.section}>
                        <View style={styles.sectionTitleWrapper}>
                            <Text style={styles.sectionTitle}>{section.title}</Text>
                        </View>
                        <View style={styles.optionsWrapper}>
                            {section.options.map((option) => {
                                const isSelected = selectedValues[section.key] === option.value;
                                return (
                                    <TouchableOpacity
                                        key={option.value}
                                        style={styles.option}
                                        onPress={() => onSelectOption(section.key, option.value)}
                                    >
                                        <View style={styles.radio}>
                                            {isSelected && <View style={styles.selectedDot} />}
                                        </View>
                                        <Text style={styles.optionLabel}>{option.label}</Text>
                                    </TouchableOpacity>
                                );
                            })}
                        </View>
                    </View>
                ))}

                <View style={styles.buttonRow}>
                    <TouchableOpacity onPress={onClose} style={styles.leftButton}>
                        <Text style={styles.cancel}>{cancelText}</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={onApply} style={styles.rightButton}>
                        <Text style={styles.apply}>{applyText}</Text>
                    </TouchableOpacity>
                </View>

            </View>
        </Popover>
    );
};
const styles = StyleSheet.create({
    container: {
        backgroundColor: '#fff',
        borderRadius: 12,
        flex: 1
    },
    innerContainer: {
        width: Dimensions.get('window').width-10
        // minWidth: 280
    },
    arrow: {
        borderTopColor: '#fff',
    },
    sectionTitleWrapper: {
        paddingLeft: 20,
        paddingRight: 20,
        backgroundColor: "#F8F8F8"
    },
    sectionTitle: {
        fontWeight: 'bold',
        fontSize: 16,
        color: '#333',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: "#C8C7CC",
    },
    optionsWrapper: {
        paddingHorizontal: 20
    },
    option: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 10,
        borderBottomWidth: 1,
        borderBottomColor: "#ddd"
    },
    radio: {
        width: 20,
        height: 20,
        borderRadius: 10,
        borderWidth: 2,
        borderColor: '#25b2bd',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 10,
    },
    selectedDot: {
        width: 10,
        height: 10,
        borderRadius: 5,
        backgroundColor: '#25b2bd',
    },
    optionLabel: {
        fontSize: 15,
        color: '#333',
    },
    buttonRow: {
        flexDirection: 'row',
        borderTopWidth: 1,
        borderTopColor: '#ddd',
        marginTop: 12,
        paddingVertical: 12,
    },
    leftButton: {
        flex: 1,
        alignItems: 'center',
        borderRightWidth: 1,
        borderRightColor: '#C8C7CC',
    },

    rightButton: {
        flex: 1,
        alignItems: 'center',
    },

    cancel: {
        color: '#25b2bd',
        fontWeight: 'bold',
        fontSize: 16,
    },

    apply: {
        color: '#25b2bd',
        fontWeight: 'bold',
        fontSize: 16,
    },

});


export default SortPopupModal;