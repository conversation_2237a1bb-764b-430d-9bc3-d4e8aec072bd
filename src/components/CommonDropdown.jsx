import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';

const CommonDropdown = memo(({
  data = [],
  placeholder = 'Select...',
  search = true,
  headerValue = 'Select an option',
  value = null,
  onChange = () => {},
  style = {}
}) => {
  // Default item renderer
  const renderDropdownItem = (item) => {
    if (item.header === true) {
      return (
        <View style={styles.dropdownHeaderItemContainer}>
          <Text style={styles.dropdownHeaderItemText}>{headerValue}</Text>
        </View>
      );
    }
    return (
      <View style={styles.dropdownItemContainer}>
        <Text style={styles.dropdownItemText}>{item.label}</Text>
      </View>
    );
  };

  // Handle item selection
  const handleItemSelect = (item) => {
    onChange(item);
  };

  return (
    <Dropdown
      data={data}
      labelField="label"
      valueField="value"
      placeholder={placeholder}
      value={value}
      style={[styles.input, style]}
      search={search}
      searchPlaceholder="Search..."
      renderItem={renderDropdownItem}
      onChange={handleItemSelect}
      maxHeight={300}
    />
  );
});

const styles = StyleSheet.create({
  input: {
    height: 50,
    borderColor: 'gray',
    borderWidth: 0.5,
    borderRadius: 8,
    paddingHorizontal: 8,
    marginBottom: 10,
    backgroundColor: 'white',
  },
  placeholderStyle: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  selectedTextStyle: {
    fontSize: 16,
    color: '#111827',
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
    paddingHorizontal: 8,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  dropdownContainer: {
    borderRadius: 8,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    overflow: 'hidden',
  },
  // Header item style
  dropdownHeaderItemContainer: {
    backgroundColor: '#F5F5F5',
    paddingVertical: 10,
    paddingLeft: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  dropdownHeaderItemText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4B5563',
  },
  // Regular item style
  dropdownItemContainer: {
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingLeft: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  dropdownItemText: {
    fontSize: 16,
    color: '#111827',
  },
  // Active item style
  dropdownItemContainerActive: {
    backgroundColor: '#F3F4F6',
  },
  dropdownItemTextActive: {
    color: '#111827',
  },
});

export default CommonDropdown;
