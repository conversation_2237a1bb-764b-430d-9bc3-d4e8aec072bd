import React, { useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { responsive } from '../utils/responsive';
import colors from '../theme/colors';
import LanguageContext from '../context/LanguageContext';
import BackIcon from '../assets/img/backgreen.svg';

const CommonHeader = ({ title, onBackPress }) => {
    const { t } = useContext(LanguageContext);

    return (
        <View style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
                <BackIcon width={20} height={20} />
                <Text style={styles.backText}>{t.t('Back')}</Text>
            </TouchableOpacity>
            <Text style={styles.headerTitle}>{title}</Text>
        </View>
    );
};

export default CommonHeader;

const styles = StyleSheet.create({
    header: {
        flexDirection: 'column',
        paddingHorizontal: responsive(5),
        paddingTop: responsive(20),
        paddingBottom: responsive(10),
        backgroundColor: colors.header.background,
        borderBottomWidth: 1,
        borderBottomColor: colors.border.main,
    },
    backButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 6,
    },
    backText: {
        fontSize: 16,
        color: colors.text.navBarGreen,
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: '600',
        color: '#000',
        paddingLeft: 10,
        marginTop: 5
    },
});
