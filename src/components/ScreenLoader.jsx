import React from "react";
import { View, ActivityIndicator, Text, StyleSheet } from "react-native";

const ScreenLoader = ({ visible = false, message = "Loading..." }) => {
    if (!visible) return null;

    return (
        <View style={styles.overlay}>
            <ActivityIndicator size="large" color="#7FC5C6" />
            {message ? <Text style={styles.message}>{message}</Text> : null}
        </View>
    );
};

export default ScreenLoader;

const styles = StyleSheet.create({
    overlay: {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
    },
    message: {
        marginTop: 10,
        fontSize: 16,
        color: "black",
    },
});
