{"name": "th<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "expo": "^53.0.22", "expo-constants": "^17.1.7", "expo-font": "~13.3.2", "expo-localization": "^16.1.6", "expo-router": "~5.1.5", "expo-status-bar": "~2.2.3", "i18n-js": "^4.5.1", "react": "19.0.0", "react-hook-form": "^7.61.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.11", "react-native-element-dropdown": "^2.12.4", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-modal": "^14.0.0-rc.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-popover-view": "^6.1.0", "react-native-progress": "^5.0.1", "react-native-safe-area-context": "5.4.0", "react-native-svg": "15.11.2", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "zod": "^4.0.8", "expo-dev-client": "~5.2.4", "expo-linking": "~7.1.7", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "react-native-svg-transformer": "^1.5.1"}, "private": true}